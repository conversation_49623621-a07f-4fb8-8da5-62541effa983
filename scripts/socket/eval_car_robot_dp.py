import os
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
device='cuda:0'
import torch
import argparse
import numpy as np

from x2robot_infer.eval.utils import interpolates_actions
from x2robot_infer.eval.robot_controller import <PERSON><PERSON>ontroller
import hydra
import dill
from diffusion_policy.policy.base_image_policy import BaseImagePolicy
from diffusion_policy.workspace.base_workspace import BaseWorkspace
from x2robot_infer.utils import ArmAction<PERSON>istory

def main(args):
    # load model
    ckpt_path = args.checkpoint_path
    action_interpolate_multiplier = args.action_interpolate_multiplier
    action_start_ratio = args.action_start_ratio
    action_end_ratio = args.action_end_ratio
    print(f'ckpt_path:{ckpt_path}')
    payload = torch.load(open(ckpt_path, 'rb'), pickle_module=dill)
    cfg = payload['cfg']
    cls = hydra.utils.get_class(cfg._target_)

    rgb_keys = list()
    lowdim_keys = list()
    tactile_keys = list()
    obs_shape_meta = cfg.shape_meta.obs
    for key, attr in obs_shape_meta.items():
        type = attr.get('type', 'low_dim')
        if type == 'rgb':
            rgb_keys.append(key)
        elif type == 'low_dim':
            lowdim_keys.append(key)
        elif type == 'tactile':
            tactile_keys.append(key)

    workspace = cls(cfg, cfg.multi_run.run_dir)
    workspace: BaseWorkspace
    workspace.load_payload(payload, exclude_keys=None, include_keys=None)
    # diffusion model
    policy: BaseImagePolicy
    policy = workspace.model
    policy.eval().to(device)
    policy.num_inference_steps = 100
    low_dim_obs_horizon = cfg.task.low_dim_obs_horizon
    img_obs_horizon = cfg.task.img_obs_horizon
    action_dim = cfg.task.shape_meta.action.shape[0]
    action_history_length = cfg.task.action_history_length if hasattr(cfg.task, 'action_history_length') else 0
    use_tactile = len(tactile_keys) > 0
    print(f'use_tactile: {use_tactile}')

    # Add this after loading the model configuration
    use_gripper_cur = cfg.task.use_gripper_cur if hasattr(cfg.task, 'use_gripper_cur') else False
    print(f'use_gripper_cur: {use_gripper_cur}')

    history_len = 0
    if action_history_length > 0:
        history_len = action_history_length 
        arm_history = ArmActionHistory(max_length=history_len)
        print(f'history len: {history_len}')
        for _ in range(history_len):
            arm_history.add_action([0.0]*action_dim)
 

    def _pred_func(self, views, actions) -> dict:
        action_keys = list(actions.keys())
        # print("recivied actions:", action_keys, flush=True)

        left_agent_data = actions['follow1_pos'] # (7)
        right_agent_data = actions['follow2_pos'] # (7)
        
        if use_gripper_cur:
            left_joint_cur = actions['follow1_joints_cur'][-1:] # (7)
            right_joint_cur = actions['follow2_joints_cur'][-1:] # (7)
            left_joint_cur = np.array(left_joint_cur).reshape(-1,1)
            right_joint_cur = np.array(right_joint_cur).reshape(-1,1)
        else:
            left_joint_cur = None
            right_joint_cur = None 

        # Handle tactile data if available
        tactile_data = None
        if use_tactile and 'tactile_data_left' in actions and 'tactile_data_right' in actions:
            tactile_left = actions['tactile_data_left']
            tactile_right = actions['tactile_data_right']
            tactile_data = np.concatenate([tactile_left, tactile_right], axis=-1, dtype=np.float32)
            # print("tactile_data shape:", tactile_data.shape, flush=True)
            
        if "head_pos" in actions:
            head_pos = actions["head_pos"]
        else:
            head_pos = [0.0,-1.0]
        if "lifting_mechanism_position" in actions:
            height = actions["lifting_mechanism_position"]
        else:
            height = [0.0]
        if "car_pose" in actions:
            car_pose = actions["car_pose"]
        else:
            car_pose = [0.0, 0.0]
        # print(f'head_pos:{head_pos}, height:{height}, car_pose:{car_pose}')

        agent_data = [left_agent_data, right_agent_data]
        agent_data = np.concatenate(agent_data).reshape(1,action_dim)
        # print("agent_data:",agent_data.shape)

        if history_len > 0:
            history_data = arm_history.get_history()
            agent_data = np.concatenate([history_data, agent_data], axis=0)
        if use_gripper_cur:
            agent_pos = np.concatenate([agent_data, left_joint_cur, right_joint_cur], axis=-1).reshape(-1, action_dim+2)
        else:
            agent_pos = np.array(agent_data).reshape(-1, action_dim)
        obs = {
            'face_view': views['camera_front'],
            'right_wrist_view': views['camera_right'],
            'left_wrist_view': views['camera_left'],
            'agent_pos': agent_pos,
        }

        # TODO: 需要时再添加
        head_action_pred = None
        height_action_pred = None
        car_action_pred = None
        with torch.no_grad():
            obs_dict = {k:torch.from_numpy(v).unsqueeze(0).to(device) for k,v in obs.items() if k in rgb_keys or k in lowdim_keys}
            batch = {'obs': obs_dict}
            
            # Add tactile data to batch if available
            if use_tactile and tactile_data is not None:
                tactile_tensor = torch.from_numpy(tactile_data).unsqueeze(0).unsqueeze(0).to(device)
                batch['tactile'] = tactile_tensor
                
            result = policy.predict_action(batch)  
            action_pred = result['action_pred'][0].detach().to('cpu').numpy()
            left_action_pred = action_pred[:,:7]
            right_action_pred = action_pred[:,7:14]

        if history_len > 0:
            for action in action_pred.tolist():
                arm_history.add_action(action)

        inter_len = action_interpolate_multiplier*len(left_action_pred)
        left_action_pred = interpolates_actions(actions=left_action_pred, num_actions=left_action_pred.shape[0], target_num_actions=inter_len, action_dim=7)
        right_action_pred = interpolates_actions(actions=right_action_pred, num_actions=right_action_pred.shape[0], target_num_actions=inter_len, action_dim=7)
        start_frame = int(action_start_ratio*inter_len)
        end_frame = int(action_end_ratio*inter_len)
        left_action_pred = left_action_pred[start_frame:end_frame]
        right_action_pred = right_action_pred[start_frame:end_frame]

        # 调整follow gripper
        follow1 = left_action_pred
        follow2 = right_action_pred
        follow1[:,-1] = np.maximum(follow1[:,-1]-0.0, 0.0)
        follow2[:,-1] = np.maximum(follow2[:,-1]-0.0, 0.0)
        follow1 = follow1.tolist()
        follow2 = follow2.tolist()

        serialized_actions = {
            "follow1_pos":follow1,
            "follow2_pos":follow2,
            "follow1_joints":follow1,
            "follow2_joints":follow1
        }
        if head_action_pred is not None:
            # serialized_actions["head_ops"] = head_action_pred.tolist()
            serialized_actions["head_pos"] = head_action_pred.tolist()
        else:
            # serialized_actions["head_ops"] = [[0.0,-1.0]]*len(follow1)
            serialized_actions["head_pos"] = [[0.0,-1.0]]*len(follow1)

        if height_action_pred is not None:
            serialized_actions["lifting_mechanism_position"] = height_action_pred.tolist()
        else:
            serialized_actions["lifting_mechanism_position"] = [[0.0]]*len(follow1)

        if car_action_pred is not None:
            serialized_actions["car_pose"] = car_action_pred.tolist()
        else:
            serialized_actions["car_pose"] = [[0.0,0.0]]*len(follow1)

        # print("serialized_actions:",serialized_actions.keys(),flush=True)
        # for k,v in serialized_actions.items():
        #     print(k,len(v),len(v[0]),flush=True)

        if use_gripper_cur:
            left_joint_cur = action_pred[:,-1:]  # (1) - last joint current
            right_joint_cur = action_pred[:,7:-1]  # (1) - last joint current
            print(f'Gripper current values - Left: {left_joint_cur}, Right: {right_joint_cur}')
        else:
            left_joint_cur = None
            right_joint_cur = None

        return serialized_actions
    
    RobotController.prediction = _pred_func
    robot_controller = RobotController(robot_id = args.robot_id, max_time_step=args.max_time_step)
    robot_controller.connect()
    print("robot connected")
    robot_controller.run(record_mode = args.record_mode)
    print("robot closing")
    robot_controller.close()


if __name__ == "__main__":
    # read args
    parser = argparse.ArgumentParser()
    parser.add_argument("--robot_id", type=int, default=0)
    parser.add_argument("--max_time_step", type=int, default=1000000)
    parser.add_argument("--record_mode", action='store_true')

    parser.add_argument("--config_path", type=str, default=None)
    parser.add_argument("--checkpoint_path", type=str, default=None)
    parser.add_argument("--instructions", type=str, default=None)

    parser.add_argument("--use_ema", action='store_true')
    parser.add_argument("--action_start_ratio", type=float, default=0.1)
    parser.add_argument("--action_end_ratio", type=float, default=1.0) ## TODO: 默认值应该是1.0， 测试时设为0.3以便快速结束
    parser.add_argument("--action_interpolate_multiplier", type=int, default=10)
    parser.add_argument("--use_tactile", action='store_true')

    args = parser.parse_args()

    # 20250226
    # args.checkpoint_path = "/x2robot/ganruyi/workspace/diffusion_policy/2025.02.24/00.52.05_resnet18_pick_cup_car_p40/checkpoints/epoch=0325-train_loss=0.000.ckpt"
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.02.24/00.52.49_resnet18_pick_waste_h40p20/checkpoints/epoch=0105-train_loss=0.001.ckpt'

    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.02.24/22.18.20_resnet18_pick_eggs_p40/checkpoints/epoch=0195-train_loss=0.001.ckpt'
    # args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.02.24/22.23.13_resnet18_notactile_pick_eggs_p40/checkpoints/epoch=0195-train_loss=0.001.ckpt'

    # 20250228
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.02.26/23.23.23_resnet50_pretrained_pick_cup_car_p40/checkpoints/epoch=0095-train_loss=0.000.ckpt' # epoch80效果还比较正常了，就是执行比较慢,95epoch会更好一点
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.02.24/00.52.49_resnet18_pick_waste_h40p20/checkpoints/epoch=0190-train_loss=0.001.ckpt'

    # 20250303
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.02.26/23.23.23_resnet50_pretrained_pick_cup_car_p40/checkpoints/epoch=0315-train_loss=0.000.ckpt'

    # args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.03.05/14.34.57_resnet50_pretrained_pick_cup_car_p40/checkpoints/epoch=0025-train_loss=0.004.ckpt' # 左右臂反了
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.03.06/16.49.19_resnet50_pretrained_pick_cup_car_p40/checkpoints/epoch=0020-train_loss=0.002.ckpt' # 正常了
    # 20250311
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.03.06/16.49.19_resnet50_pretrained_pick_cup_car_p40/checkpoints/epoch=0120-train_loss=0.001.ckpt'
    
    # 20250312
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.03.09/01.34.04_resnet18_factory_pickitem_p40/checkpoints/epoch=0195-train_loss=0.000.ckpt' # 工厂回放数据训练的模型
    
    # 20250312
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.03.11/23.35.00_resnet50_pretrained_pick_cup_car_p40_10015/checkpoints/epoch=0100-train_loss=0.001.ckpt'

    # 20250313
    args.checkpoint_path = '/ckpt/epoch=0160-train_loss=0.001.ckpt'
    args.checkpoint_path = '/ckpt/epoch=0065-train_loss=0.000.ckpt'
    args.instructions = ["test test"]
    args.action_start_ratio = 0.1
    args.action_end_ratio = 0.9
    args.robot_id = 10 # 10000 是远程推理服务器, 10001 是本地服务器
    args.action_interpolate_multiplier = 30
    main(args)  
