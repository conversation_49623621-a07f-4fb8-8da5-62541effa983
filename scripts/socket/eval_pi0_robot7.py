import argparse
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
device='cuda:0'

import socket
import matplotlib.pyplot as plt

from typing import Tuple, Sequence, Dict, Union, Optional, Callable
import torch
import torch.nn as nn
from diffusers.schedulers.scheduling_ddpm import DDPMScheduler
from diffusers.training_utils import EMAModel
from diffusers.optimization import get_scheduler
from tqdm.auto import tqdm
import cv2
import torchvision
import torchvision.transforms as tv_transform
import struct
import time
from multiprocessing.managers import SharedMemoryManager
import click
import cv2
import numpy as np
import torch
import dill
import hydra
import pathlib
import json
from omegaconf import OmegaConf
from diffusion_policy.common.precise_sleep import precise_wait
from diffusion_policy.real_world.real_inference_util import move_axis_only_real_obs_dict
from diffusion_policy.common.pytorch_util import dict_apply
from diffusion_policy.workspace.base_workspace import BaseWorkspace
from diffusion_policy.policy.base_image_policy import BaseImagePolicy
from diffusion_policy.common.cv2_util import get_image_transform

OmegaConf.register_new_resolver("eval", eval, replace=True)
### client: here
### server: python3 socketTest.py in ************:/home/<USER>/prj/prototype1/src/prototype1_communicationPort/scripts

## model
from collections import deque


# Function to receive a certain amount of data
def recvall(sock, count):
    buf = b''
    while count:
        newbuf = sock.recv(count)
        if not newbuf: return None
        buf += newbuf
        count -= len(newbuf)
    return buf

def read_img(conn,i,save_path,count=0):
    image_size = struct.unpack('<L', conn.recv(4))[0]
    image = recvall(conn, image_size)
    nparr = np.frombuffer(image, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    # cv2.imwrite("image-{i}-{count}.jpg", image) #
    return image

def record_data(image1,image2,image3,all_output,all_pred,save_path,count):
    cv2.imwrite(save_path+f"image-1-{count}.jpg", image1)
    cv2.imwrite(save_path+f"image-2-{count}.jpg", image2)
    cv2.imwrite(save_path+f"image-3-{count}.jpg", image3)
    if len(all_output)%10==0:
        with open(save_path+"all_output.json","w") as f:
            json.dump(all_output,f)
        with open(save_path+"all_pred.json","w") as f:
            json.dump(all_pred,f)

def recv_all(sock, count):
    buf = b''
    while count:
        newbuf = sock.recv(count)
        if not newbuf: return None
        buf += newbuf
        count -= len(newbuf)
    return buf

import numpy as np
from scipy.spatial.transform import Rotation as R
def interpolates_actions(actions, num_actions=20, target_num_actions = 80, action_dim=7):
    # 假设 actions 是你的动作序列，shape 为 [num_actions, action_dim]
    # 其中，欧拉角为 actions[:, 3:6]
    # return interpolated_actions 现在包含了插值后的动作序列，其中角度使用了球面插值
    # 生成目标动作序列的索引
    original_indices = np.linspace(0, num_actions - 1, num_actions)
    target_indices = np.linspace(0, num_actions - 1, target_num_actions)
    # 初始化插值后的动作序列数组
    interpolated_actions = np.zeros((target_num_actions, action_dim))
    if action_dim == 2: # 头部动作直接线性插值
        for i in range(action_dim):
            interpolated_actions[:, i] = np.interp(target_indices, original_indices, actions[:, i])
        return interpolated_actions

    # 对[x, y, z, gripper]使用线性插值
    for i in range(3):
        interpolated_actions[:, i] = np.interp(target_indices, original_indices, actions[:, i])
    interpolated_actions[:, -1] = np.interp(target_indices, original_indices, actions[:, -1])
    # 将欧拉角转换为四元数
    quaternions = R.from_euler('xyz', actions[:, 3:6]).as_quat()  # shape: [num_actions, 4]
    # 初始化插值后的四元数数组
    interpolated_quats = np.zeros((target_num_actions, 4))
    # 对四元数进行球面插值
    for i in range(4):  # 对四元数的每个分量进行插值
        interpolated_quats[:, i] = np.interp(target_indices, original_indices, quaternions[:, i])
    # 四元数规范化，确保插值后仍为单位四元数
    interpolated_quats = interpolated_quats / np.linalg.norm(interpolated_quats, axis=1, keepdims=True)
    # 将插值后的四元数转换回欧拉角
    interpolated_eulers = R.from_quat(interpolated_quats).as_euler('xyz')  # shape: [target_num_actions, 3]
    # 更新插值后动作序列的角度部分
    interpolated_actions[:, 3:6] = interpolated_eulers
    # print(interpolated_actions.shape)
    return interpolated_actions
    

def main(args):
    device = torch.device('cuda')
    setting = "test"
    os.makedirs(f"saved/{setting}", exist_ok=True)
    save_path = f"saved/{setting}/"

    # 创建一个TCP/IP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.setblocking(True) #设置通信是阻塞式
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    ip = '*************'
    if args.robot == 0:
        sock.bind((ip, 57749))  # 绑定到本地空闲端口
    elif args.robot == 1:
        sock.bind((ip, 57750))  # 绑定到本地空闲端口
    elif args.robot ==2:
        sock.bind((ip, 57751))  # 绑定到本地空闲端口
    elif args.robot ==4:
        sock.bind((ip, 57771))  # 绑定到本地空闲端口
    elif args.robot ==7:
        # sock.bind((ip, 57752))  # 绑定到本地空闲端口
        sock.bind((ip, 10800))  # 绑定到本地空闲端口
    sock.listen(1)  # 监听连接
    print(f"Listening on port {sock.getsockname()[1]}")

    # 接受一个连接
    conn, addr = sock.accept()
    print(f"Accepted connection from {addr}")

    data_dir ={"follow1":[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],"follow2":[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}
    data_str = json.dumps(data_dir)
    data_bytes = data_str.encode('utf-8')

    image1 = 0
    image2 = 0
    image3 = 0
    max_time_step = args.max_time_step
    count = 0
    # get config
    while True:
        if count>max_time_step:
            break
        count+=1
        time_start = time.time()
        data_size = struct.unpack('<L', conn.recv(4))[0]
        data = conn.recv(data_size)
        # print('byte data', data)
        action_data = json.loads(data.decode('utf8'))
        # print(action_data)
        left_agent_data = action_data['follow1_pos'] # (7)
        right_agent_data = action_data['follow2_pos'] # (7)
        image1 = read_img(conn,1,save_path,count) # left
        image2 = read_img(conn,2,save_path,count) # front
        image3 = read_img(conn,3,save_path,count) # right

        time_recv = time.time()
        print(f'recv time:{time_recv-time_start}')

        h,w,c = np.array(image1).shape
        obs = {
            'face_view': camera_front,
            'left_wrist_view': camera_left,
            'right_wrist_view': camera_right,
            'agent_pos': agent_pos,
        }
        # policy.reset()
        # run inference
        time_preprocess = time.time()
        print(f'pre process time:{time_preprocess-time_recv}')
        with torch.no_grad():
            s = time.time()
        time_infer = time.time()
        print(f'Inference latency:{time_infer - time_preprocess}')

        # action_pred = action_pred[-14:,:]
        # print(f'pred:{action_pred}')
        actions_factor = 20
        # interpolates actions
        action_num = action_pred.shape[0]
        left_action_pred = interpolates_actions(actions=action_pred[:,:7], num_actions=action_pred.shape[0], target_num_actions=actions_factor*action_num, action_dim=7)
        right_action_pred = interpolates_actions(actions=action_pred[:,7:14], num_actions=action_pred.shape[0], target_num_actions=actions_factor*action_num, action_dim=7)
        action_pred = np.concatenate([left_action_pred,right_action_pred], axis=1)
        # pass
        follow1 = action_pred[:, :7] # left EEF
        follow2 = action_pred[:, 7:14] # right EEF
        follow1[:,-1] -= 0.1
        follow1[:,-1] = np.maximum(follow1[:,-1], 0.0)
        follow2[:,-1] -= 0.1
        follow2[:,-1] = np.maximum(follow2[:,-1], 0.0)
        follow1 = follow1.tolist()
        follow2 = follow2.tolist()
        data_dir ={"follow1_pos":follow1,"follow2_pos":follow2,"follow1_joints":follow1,"follow2_joints":follow1}
        data_str = json.dumps(data_dir)
        data_bytes = data_str.encode('utf-8')
        time_postprocess = time.time()
        print(f'post process time: {time_postprocess-time_infer}')
        conn.sendall(struct.pack('<L', len(data_bytes)))
        conn.sendall(data_bytes)

        time_end = time.time()
        print(f'all time: {time_end-time_start}')

    sock.close()

if __name__ == "__main__":
    # read args
    parser = argparse.ArgumentParser()
    parser.add_argument("--robot", type=int, default=0)
    parser.add_argument("--checkpoint_path", type=str, default=None)
    parser.add_argument("--max_time_step", type=int, default=30000)
    parser.add_argument("--action_chunk", type=int, default=20)
    parser.add_argument("--action_hist", type=int, default=1)
    parser.add_argument("--instruction", type=str, default=None)
    args = parser.parse_args()
    # ui,ctrl+7, x2robot:wallx_roy, dp: multi_mask
    # 20250103 抓东西
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2024.12.26/23.21.49_resnet18_robot7_pickitem/checkpoints/epoch=0495-train_loss=0.000.ckpt' # 20250121 抓垃圾的任务
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.01.14/21.32.17_resnet18_pick_waste/checkpoints/epoch=0365-train_loss=0.001.ckpt' # 效果不太行，一个是下不去，另外一个是左右手有明显的停顿，考虑检查数据，增加历史，去掉静止

    # 20250124
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.01.23/15.36.45_resnet18_pick_waste_hist2s/checkpoints/epoch=0035-train_loss=0.004.ckpt'

    # 20250205
    args.checkpoint_path = '/x2robot/ganruyi/workspace/diffusion_policy/2025.01.23/15.36.45_resnet18_pick_waste_hist2s/checkpoints/epoch=0305-train_loss=0.001.ckpt'

    args.robot = 7
    main(args)