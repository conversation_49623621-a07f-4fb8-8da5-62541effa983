#!/bin/bash

git submodule update  --init --remote --force --recursive

# 镜像标签
IMAGE_TAG="harbor.zbl.local/x2robot_infer/diffusion_policy:XXXX"

# 解析代理参数
PROXY=""
while [[ $# -gt 0 ]]; do
  case "$1" in
    --proxy)
      PROXY="$2"
      shift 2
      ;;
    *)
      echo "未知参数: $1"
      exit 1
      ;;
  esac
done

# 构建命令
BUILD_CMD="docker build -t $IMAGE_TAG"

# 添加代理参数（如果存在）
if [ -n "$PROXY" ]; then
  BUILD_CMD+=" --build-arg HTTP_PROXY=$PROXY --build-arg HTTPS_PROXY=$PROXY --build-arg NO_PROXY=localhost,127.0.0.1,docker-registry.example.com,docker.io,.corp,core.harbor.domain"
fi

# 添加 Dockerfile 路径
BUILD_CMD+=" ."

# 执行构建
echo "构建命令: $BUILD_CMD"
eval "$BUILD_CMD"
