# K8s容器内和调度端脚本

## 访问配置

1、确保执行脚本的用户目录下已经存在k8s的配置文件，即`~/.kube/config`

2、确保机器在公司内网，可以访问到k8s集群。

3、确保用于执行脚本的机器已经配置了DNS，让机器可以解析K8s的服务名，在`/etc/resolv.conf`中添加一行

```yaml
nameserver 10.43.0.10
```

## 模型部署

模型部署分两个步骤：

### 填写参数

参考`diffusion_policy/config.yaml`，编写config，需要修改的是：`name`，`download_url`，`env_vars`

确保download_url是可以下载的路径。

这里的name是模型下载和模型路径的唯一标识符，需要在系统中确认不会出现重复。

env_vars开放给算法同学进行修改。

### 部署/更新模型

我们提供了统一的模型操作脚本，支持两种操作模式：

**部署新模型**

```
python ./deploy_model.py -c <config_path>
```

**更新现有模型**

```
python ./deploy_model.py -c <config_path>
```

脚本会自动检测是部署新模型还是更新现有模型，均会检查本地是否存在checkpoint文件，如不存在或文件大小不匹配，会自动下载。下载失败会删除不完整文件并重试。并且有以下特性：


1、可通过-t参数设置超时时间（默认300秒）

2、部署时会等待服务完全就绪

3、更新时会跟踪滚动更新进度直至完成

## 模型管理

### 部署就绪检查

在执行完毕模型部署的脚本后，需要判断模型是否部署完毕，目前有两种方式。

第一种是指定模型名`name`和作用域`namespace`：

```bash
python ./check_ready.py -r <name> -n <namespace>
```
由于默认的 `namespace` 就是 `deploy`，一般情况下，作用域设置为 `deploy`就可以。


第二种方法是指定配置文件：

```bash
python ./check_ready.py -c <config_path>
```

这种方式需要注意原先配置文件不要发生变化，尤其是name和namespace参数。

会得到如下输出：

```bash
# 该情况为已经部署完毕。
Ready: True

# 该情况为尚未部署完毕。
Ready: False
```


### 删除服务

服务删除可以采用两种方法，第一种是指定模型名`name`和作用域`namespace`：

```bash
python ./cleanup.py -r <name> -n <namespace>
```

第二种方法是指定配置文件：

```bash
python ./cleanup.py -f <config_path>
```