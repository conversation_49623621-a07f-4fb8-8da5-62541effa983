import argparse
import os
import yaml
from kubernetes import client, config

def load_config_data(config_path):
    """从配置文件加载资源名称和命名空间"""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Config file not found: {config_path}")
    
    with open(config_path, "r") as f:
        config_data = yaml.safe_load(f)
    
    required_fields = ['name']
    for field in required_fields:
        if field not in config_data:
            raise ValueError(f"Missing required field in config: {field}")
    
    return config_data['name'], config_data.get('namespace', 'default')

def delete_resources(api_apps, api_core, name, namespace):
    """删除Deployment和Service资源"""
    # 删除Deployment
    try:
        api_apps.delete_namespaced_deployment(
            name=name,
            namespace=namespace,
            propagation_policy="Foreground"
        )
        print(f"✅ Deleted Deployment: {name}")
    except client.rest.ApiException as e:
        if e.status != 404:
            raise

    # 删除Service
    try:
        api_core.delete_namespaced_service(
            name=f"{name}-service",
            namespace=namespace
        )
        print(f"✅ Deleted Service: {name}-service")
    except client.rest.ApiException as e:
        if e.status != 404:
            raise

def main():
    parser = argparse.ArgumentParser(
        description="Kubernetes Resource Cleaner",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 资源定位方式（互斥组）
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-r", "--resource", help="直接指定资源名称")
    group.add_argument("-f", "--folder", help="包含config.yaml的文件夹路径")
    group.add_argument("-c", "--config", help="配置文件路径")
    
    # 命名空间参数
    parser.add_argument("-n", "--namespace", 
                       help="指定命名空间（覆盖配置文件中的设置）",
                       default=argparse.SUPPRESS)  # 不显示默认值
    
    # 确认选项
    parser.add_argument("-y", "--yes", 
                       action="store_true",
                       help="跳过确认提示")

    args = parser.parse_args()

    try:
        # 确定资源名称和基础命名空间
        resource_name = ""
        base_namespace = "default"
        
        if args.resource:
            resource_name = args.resource
        elif args.folder:
            config_path = os.path.join(args.folder, "config.yaml")
            resource_name, base_namespace = load_config_data(config_path)
        elif args.config:
            resource_name, base_namespace = load_config_data(args.config)
        
        # 确定最终命名空间（命令行参数优先）
        final_namespace = getattr(args, 'namespace', base_namespace)
        
        # 确认提示
        if not args.yes:
            confirm = input(
                f"确定要删除资源 [{resource_name}] 及其关联服务（命名空间: {final_namespace}）吗？ [y/N]: "
            )
            if confirm.lower() != "y":
                print("🚫 操作已取消")
                return

        # 初始化客户端
        config.load_kube_config()
        apps_v1 = client.AppsV1Api()
        core_v1 = client.CoreV1Api()
        
        # 执行清理
        delete_resources(apps_v1, core_v1, resource_name, final_namespace)
        print(f"🎉 清理完成 [Namespace: {final_namespace}]")

    except FileNotFoundError as e:
        print(f"❌ 文件错误: {str(e)}")
    except yaml.YAMLError as e:
        print(f"❌ YAML解析错误: {str(e)}")
    except client.rest.ApiException as e:
        print(f"❌ Kubernetes API错误 ({e.status}): {e.reason}")
    except Exception as e:
        print(f"❌ 意外错误: {str(e)}")

if __name__ == "__main__":
    main()