name: "model"
backend: "python"

input [
  {
    name: "ACTION_FOLLOW1_POS"
    data_type: TYPE_FP32
    dims: [ 7 ]
  },
  {
    name: "ACTION_FOLLOW2_POS"
    data_type: TYPE_FP32
    dims: [ 7 ]
  },
  {
    name: "ACTION_FOLLOW1_JOINTS_CUR"
    data_type: TYPE_FP32
    dims: [ 7 ]
    optional: true
  },
  {
    name: "ACTION_FOLLOW2_JOINTS_CUR"
    data_type: TYPE_FP32
    dims: [ 7 ]
    optional: true
  },
  {
    name: "CAR_POSE"
    data_type: TYPE_FP32
    dims: [ 3 ]
    optional: true
  },
  {
    name: "LIFT"
    data_type: TYPE_FP32
    dims: [ 1 ]
    optional: true
  },
  {
    name: "CAMERA_LEFT"
    data_type: TYPE_STRING
    dims: [ 1 ]
    optional: true
  },
  {
    name: "CAMERA_FRONT"
    data_type: TYPE_STRING
    dims: [ 1 ]
  },
  {
    name: "CAMERA_RIGHT"
    data_type: TYPE_STRING
    dims: [ 1 ]
  },
  {
    name: "CONTROL_MODE"
    data_type: TYPE_STRING
    dims: [ 1 ]
    optional: true
  },
  {
    name: "HEAD_POS"
    data_type: TYPE_FP32
    dims: [ 2 ]
    optional: true
  },
  {
    name: "INSTRUCTION"
    data_type: TYPE_STRING
    dims: [ 1 ]
    optional: true
  }
]

output [
  {
    name: "FOLLOW1_POS"
    data_type: TYPE_FP32
    dims: [ -1, 7 ]
  },
  {
    name: "FOLLOW2_POS"
    data_type: TYPE_FP32
    dims: [ -1, 7 ]
  },
  {
    name: "FOLLOW1_JOINTS"
    data_type: TYPE_FP32
    dims: [ -1, 7 ]
  },
  {
    name: "FOLLOW2_JOINTS"
    data_type: TYPE_FP32
    dims: [ -1, 7 ]
  },
  {
    name: "HEAD_POS"
    data_type: TYPE_FP32
    dims: [ -1, 2 ]
  },
  {
    name: "CAR_POSE_OUT"
    data_type: TYPE_FP32
    dims: [ -1, 3 ]
  },
  {
    name: "LIFT_OUT"
    data_type: TYPE_FP32
    dims: [ -1, 1 ]
  }
]

parameters {
  key: "ckpt_path"
  value: {
    #string_value: "/workspace/fold_towel_epoch80-train_loss=0.001.ckpt"
    string_value: "/workspace/tidy_clothes_epoch=0055-train_loss=0.001.ckpt"
  }
}

parameters {
  key: "action_interpolate_multiplier"
  value: {
    string_value: "10"
  }
}

parameters {
  key: "action_start_ratio"
  value: {
    string_value: "0.0"
  }
}

parameters {
  key: "action_end_ratio"
  value: {
    string_value: "1.0"
  }
}

parameters {
  key: "instruction"
  value: {
    string_value: "${instruction}"
  }
}

parameters {
  key: "control_mode"
  value: {
    string_value: "position"
  }
}



instance_group [
  {
    kind: KIND_GPU
    count: 1
    gpus: [0]
  }
]
