# config.yaml
name: starrick-test
image: harbor.zbl.local/x2robot_infer/diffusion_policy:1.04-ins
namespace: deploy
backend_path: "/home/<USER>/workspace/x2robot_infer/examples/diffusion_policy/backend"
download_path: "dummy@**************:/x2robot_v2/share/ganruyi/fold_towel_epoch=0085-train_loss=0.003.ckpt"
vram_limit: 4000
env_vars:
  ACTION_INTERPOLATE_MULTIPLIER: "28"
  ACTION_START_RATIO: "0.15"
  ACTION_END_RATIO: "0.8"
  NVIDIA_DRIVER_CAPABILITIES: "all"