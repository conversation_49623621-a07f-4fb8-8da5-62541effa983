import cv2
import dns.resolver
import numpy as np
import tritonclient.http as httpclient
import tritonclient.grpc as grpcclient
from tritonclient.utils import np_to_triton_dtype
from ipaddress import ip_address
# ================== 协议适配层 ==================
class TritonInferenceClient:
    def __init__(self, ip, port=None, output_names=None, model_name=None, use_grpc=True):
        """初始化Triton客户端
        
        :param model_name: 模型名称
        :param ip: 服务器IP
        :param port: 地址的端口号
        :param output_names: 输出层名称列表
        :param use_grpc: 是否使用gRPC协议 (默认True)
        """
        self.model_name = model_name
        self.output_names = output_names
        self.use_grpc = use_grpc
        
        # ================== 自动处理DNS ==================
        if not self.ip_address(ip):
            try:
                ip = self.resolve_dns(ip)
                
            except ValueError as e:
                print(f"DNS解析{address}错误: {e}")
                exit(1)

        address = f"{ip}:{port}" if port else ip
        # 创建协议客户端
        self.client = self._create_client(address)

    def ip_address(self, ip_str: str) -> bool:
        """
        检查字符串是否是有效的IPv4地址
        有效的IPv4格式：由三个点分隔的四个十进制数，每个数范围0-255
        """
        parts = ip_str.split('.')
        
        # 检查是否正好分为4段
        if len(parts) != 4:
            return False
        
        # 检查每段是否为合法的十进制数
        for part in parts:
            # 检查是否只包含数字
            if not part.isdigit():
                return False
            
            # 转换为整数并检查范围
            num = int(part)
            if num < 0 or num > 255:
                return False
        
        return True
    
    def resolve_dns(self, hostname, dns_server='**********'):
        try:
            # 创建自定义DNS解析器
            resolver = dns.resolver.Resolver()
            # 设置指定的DNS服务器
            resolver.nameservers = [dns_server]
            
            # 查询A记录
            answer = resolver.resolve(hostname, 'A')
            ipv4 = answer[0].address
            print(f"IPv4 (A 记录) via {dns_server}: {ipv4}")
            return ipv4
            
        except dns.resolver.NXDOMAIN:
            print(f"域名不存在: {hostname}")
        except dns.resolver.Timeout:
            print("DNS查询超时")
        except dns.resolver.NoAnswer:
            print(f"没有找到A记录: {hostname}")
        except Exception as e:
            print(f"DNS解析失败: {e}")
        
    def _create_client(self, address):
        """根据协议类型创建客户端"""
        if self.use_grpc:
            return grpcclient.InferenceServerClient(address)
        else:
            return httpclient.InferenceServerClient(address)

    def _build_inputs(self, inputs_dict):
        """构建协议相关的输入对象"""
        if self.use_grpc:
            return [
                grpcclient.InferInput(name, data.shape, np_to_triton_dtype(data.dtype))
                for name, data in inputs_dict.items()
            ]
        else:
            return [
                httpclient.InferInput(name, data.shape, np_to_triton_dtype(data.dtype))
                for name, data in inputs_dict.items()
            ]

    def _build_outputs(self):
        """构建协议相关的输出请求"""
        if self.use_grpc:
            return [grpcclient.InferRequestedOutput(name) for name in self.output_names]
        else:
            return [httpclient.InferRequestedOutput(name) for name in self.output_names]

    def _parse_response(self, response):
        """解析不同协议的响应"""
        return {name: response.as_numpy(name) for name in self.output_names}

    # ================== 核心逻辑 ==================
    def infer(self, inputs):
        """执行推理请求
        
        :param inputs: 输入数据字典 {输入层名称: numpy数组}
        :return: 输出结果字典 {输出层名称: numpy数组}
        """
        # 准备输入数据
        triton_inputs = self._build_inputs(inputs)
        for input_obj in triton_inputs:
            input_name = input_obj.name()
            input_obj.set_data_from_numpy(inputs[input_name])

        # 发送推理请求
        response = self.client.infer(
            model_name=self.model_name,
            inputs=triton_inputs,
            outputs=self._build_outputs(),
        )
        return self._parse_response(response)

CAMERA_SHAPE = [1, 480, 640, 3]

def compress_image(image_np):
    """压缩单张图像（输入形状：HWC，输出：压缩后的字节流）"""
    assert image_np.ndim == 3, "输入图像应为HWC格式"
    success, encoded = cv2.imencode('.jpg', image_np)
    if not success:
        raise ValueError("图像压缩失败")
    return encoded.tobytes()

def _generate_single():
    # 生成原始图像（注意：移除批次维度生成）
    raw_img = np.random.randint(0, 256, CAMERA_SHAPE[1:], dtype=np.uint8)
    # 压缩图像
    compressed_bytes = compress_image(raw_img)
    # 转换为numpy数组（Triton要求输入为numpy类型）
    return np.array([compressed_bytes], dtype=np.object_)

if __name__ == "__main__":
    # 初始化客户端
    model_name = "model"
    server_address = "127.0.0.1"  # 根据实际服务器地址修改
    port = 8001  # 根据实际端口修改
    output_names = [
        "FOLLOW1_POS",
        "FOLLOW2_POS",
    ]

    client = TritonInferenceClient(
        model_name=model_name,
        ip=server_address,
        port=port,
        output_names=output_names,
        use_grpc=True  # 根据实际协议选择
    )

    # 准备输入数据 - 所有输入都是可选的，但至少需要提供一些数据
    inputs = {
        "ACTION_FOLLOW1_POS": np.random.rand(7).astype(np.float32),
        "ACTION_FOLLOW2_POS": np.random.rand(7).astype(np.float32),
        "ACTION_FOLLOW1_JOINTS_CUR": np.random.rand(7).astype(np.float32),
        "ACTION_FOLLOW2_JOINTS_CUR": np.random.rand(7).astype(np.float32),
        "CAMERA_LEFT": _generate_single(),
        "CAMERA_FRONT": _generate_single(),
        "CAMERA_RIGHT": _generate_single(),
        # 新增INSTRUCTION输入（字符串类型需要特殊编码）
        "INSTRUCTION": np.array(["抓物体"], dtype=np.object_)
    }

    # 执行推理
    try:
        outputs = client.infer(inputs)
        print("推理成功！输出结果:")
        for name, data in outputs.items():
            print(f"{name}: shape={data.shape}, dtype={data.dtype}")
    except Exception as e:
        print(f"推理失败: {str(e)}")
