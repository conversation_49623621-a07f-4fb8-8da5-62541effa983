import cv2
import os
from datetime import datetime
import sys
import json
import dill
import time
import torch
import hydra
import traceback

import triton_python_backend_utils as pb_utils
import numpy as np
import copy
from functools import partial

from collections import deque
from itertools import islice
from scipy.spatial.transform import Rotation as R
from diffusion_policy.policy.base_image_policy import BaseImagePolicy
from diffusion_policy.workspace.base_workspace import BaseWorkspace
from x2robot_dataset.common.data_utils import convert_6D_to_euler, convert_euler_to_6D, relative_to_actions

def interpolates_actions(actions, num_actions=20, target_num_actions=80, action_dim=7):
    # 假设 actions 是你的动作序列，shape 为 [num_actions, action_dim]
    # 其中，欧拉角为 actions[:, 3:6]
    # return interpolated_actions 现在包含了插值后的动作序列，其中角度使用了球面插值
    # 生成目标动作序列的索引
    original_indices = np.linspace(0, num_actions - 1, num_actions)
    target_indices = np.linspace(0, num_actions - 1, target_num_actions)
    # 初始化插值后的动作序列数组
    interpolated_actions = np.zeros((target_num_actions, action_dim))
    # 对[x, y, z, gripper]使用线性插值
    for i in range(3):
        interpolated_actions[:, i] = np.interp(target_indices, original_indices, actions[:, i])
    interpolated_actions[:, -1] = np.interp(target_indices, original_indices, actions[:, -1])
    # 将欧拉角转换为四元数
    quaternions = R.from_euler("xyz", actions[:, 3:6]).as_quat()  # shape: [num_actions, 4]
    # 初始化插值后的四元数数组
    interpolated_quats = np.zeros((target_num_actions, 4))
    # 对四元数进行球面插值
    for i in range(4):  # 对四元数的每个分量进行插值
        interpolated_quats[:, i] = np.interp(target_indices, original_indices, quaternions[:, i])
    # 四元数规范化，确保插值后仍为单位四元数
    interpolated_quats = interpolated_quats / np.linalg.norm(interpolated_quats, axis=1, keepdims=True)
    # 将插值后的四元数转换回欧拉角
    interpolated_eulers = R.from_quat(interpolated_quats).as_euler("xyz")  # shape: [target_num_actions, 3]
    # 更新插值后动作序列的角度部分
    interpolated_actions[:, 3:6] = interpolated_eulers
    # print(interpolated_actions.shape)
    return interpolated_actions


class ArmActionHistory:

    def __init__(self, max_length=20):
        self.history = deque(maxlen=max_length)

    def add_action(self, action):
        """
        添加一个新的动作到历史记录中
        """
        self.history.append(action)

    def get_history(self):
        """
        获取所有的动作历史列表
        """
        hist = list(self.history)
        # print(f'hist: {hist}')
        hist = np.array(hist)
        return hist

    def __len__(self):
        """
        返回当前历史记录中的动作数量
        """
        return len(self.history)


def decompress_image(byte_data):
    """解压缩单张图像（输入：字节流，输出：解压后的numpy数组 HWC格式）"""
    if byte_data is None or len(byte_data) == 0:
        return None

    # 使用cv2.imdecode解压字节流
    img = cv2.imdecode(np.frombuffer(byte_data, np.uint8), cv2.IMREAD_COLOR)
    if img is None:
        raise ValueError("图像解压失败")
    return img


class TritonPythonModel:
    def initialize(self, args):
        self.logger = pb_utils.Logger
        self.device_kind = args["model_instance_kind"]  # 设备类型：CPU 或 GPU
        self.device_id = args["model_instance_device_id"]  # 设备ID（如 "0" 表示 GPU 0）

        # 根据设备类型设置 PyTorch 的 device
        if self.device_kind == "GPU":
            self.torch_device = f"cuda:{self.device_id}"
        else:
            self.torch_device = "cpu"

        self.model_config = model_config = json.loads(args["model_config"])

        # 从环境变量或配置文件中获取 ckpt_path
        ckpt_path = os.environ.get("CKPT_PATH") or str(model_config["parameters"].get("ckpt_path", {}).get("string_value", ""))
        # 其他参数同理（注意类型转换）
        self.action_interpolate_multiplier = int(
            os.environ.get("ACTION_INTERPOLATE_MULTIPLIER")
            or model_config["parameters"].get("action_interpolate_multiplier", {}).get("string_value", 1)
        )

        self.action_start_ratio = float(
            os.environ.get("ACTION_START_RATIO") or model_config["parameters"].get("action_start_ratio", {}).get("string_value", 0.0)
        )

        self.action_end_ratio = float(
            os.environ.get("ACTION_END_RATIO") or model_config["parameters"].get("action_end_ratio", {}).get("string_value", 1.0)
        )
        # 如果没有设置 INSTRUCTION，则使用配置文件中的默认值，如果配置文件中也没有，则为None
        # 如果输入有INSTRUCTION，则使用输入的值
        self.instruction = os.environ.get("INSTRUCTION") or model_config["parameters"].get("instruction", {}).get("string_value", None)

        self.logger.log_info(f"ckpt path: {ckpt_path}")
        self.logger.log_info(f"action_interpolate_multiplier: {self.action_interpolate_multiplier}")
        self.logger.log_info(f"action_start_ratio : {self.action_start_ratio }")
        self.logger.log_info(f"action_end_ratio: {self.action_end_ratio}")
        payload = torch.load(open(ckpt_path, "rb"), pickle_module=dill)
        cfg = payload["cfg"]
        cls = hydra.utils.get_class(cfg._target_)

        self.rgb_keys = list()
        self.lowdim_keys = list()
        self.tactile_keys = list()
        obs_shape_meta = cfg.shape_meta.obs
        for key, attr in obs_shape_meta.items():
            type = attr.get("type", "low_dim")
            if type == "rgb":
                self.rgb_keys.append(key)
            elif type == "low_dim":
                self.lowdim_keys.append(key)
            elif type == "tactile":
                self.tactile_keys.append(key)

        workspace = cls(cfg, cfg.multi_run.run_dir)
        workspace: BaseWorkspace
        workspace.load_payload(payload, exclude_keys=None, include_keys=None)
        # diffusion model
        self.policy: BaseImagePolicy
        self.policy = workspace.model
        self.policy.eval().to(self.torch_device)
        self.policy.num_inference_steps = 100
        self.action_dim = cfg.task.shape_meta.action.shape[0]
        action_history_length = cfg.task.action_history_length if hasattr(cfg.task, "action_history_length") else 0
        self.use_tactile = len(self.tactile_keys) > 0

        # Add this after loading the model configuration
        self.use_gripper_cur = cfg.task.use_gripper_cur if hasattr(cfg.task, "use_gripper_cur") else False
        print(f"use_gripper_cur: {self.use_gripper_cur}")

        self.history_len = 0
        if action_history_length > 0:
            self.history_len = action_history_length
            self.arm_history = ArmActionHistory(max_length=self.history_len)
            print(f"history len: {self.history_len}")
            for _ in range(self.history_len):
                self.arm_history.add_action([0.0] * self.action_dim)

    def _pred_func(self, views, actions) -> dict:
        # action_keys = list(actions.keys())
        # print("recivied actions:", action_keys, flush=True)
        # self.logger.info所有的actions
        # for action_key, action_value in views.items():
        #     self.logger.log_info(f"Key: {action_key}, Value: {action_value.shape}")

        left_agent_data = actions["follow1_pos"]  # (7)
        right_agent_data = actions["follow2_pos"]  # (7)

        if self.use_gripper_cur:
            left_joint_cur = actions["follow1_joints_cur"][-1:]  # (7)
            right_joint_cur = actions["follow2_joints_cur"][-1:]  # (7)
            # self.logger.log_info(f"right_joint_cur: {right_joint_cur} vs -1.3")
            # if right_joint_cur<-1.0:
            #     right_joint_cur = -3.6
            left_joint_cur = np.array(left_joint_cur).reshape(-1, 1)
            right_joint_cur = np.array(right_joint_cur).reshape(-1, 1)
        else:
            left_joint_cur = None
            right_joint_cur = None

        agent_data = [left_agent_data, right_agent_data]
        agent_data = np.concatenate(agent_data).reshape(1, 14)
        # print("agent_data:",agent_data.shape)

        agent_data_6D = np.zeros(20)
        agent_data = np.concatenate([left_agent_data, right_agent_data], axis=-1, dtype=np.float32)
        raw_agent_data = np.array([agent_data])
        raw_agent_data = np.zeros((1, 14))
        raw_agent_data[0] = copy.deepcopy(agent_data)
        agent_data_6D[:3] = agent_data[:3]
        agent_data_6D[3:9] = convert_euler_to_6D(agent_data[3:6])
        agent_data_6D[9] = agent_data[6]
        agent_data_6D[10:13] = agent_data[7:10]
        agent_data_6D[13:19] = convert_euler_to_6D(agent_data[10:13])
        agent_data_6D[19] = agent_data[13]
        agent_data = agent_data_6D.reshape(1, self.action_dim)

        if self.history_len > 0:
            history_data = self.arm_history.get_history()
            agent_data = np.concatenate([history_data, agent_data], axis=0)
        if self.use_gripper_cur:
            agent_pos = np.concatenate([agent_data, left_joint_cur, right_joint_cur], axis=-1).reshape(-1, self.action_dim + 2)
        else:
            agent_pos = np.array(agent_data).reshape(-1, self.action_dim)
        obs = {
            "face_view": views["camera_front"],
            "right_wrist_view": views["camera_right"],
            "left_wrist_view": views["camera_left"],
            "agent_pos": agent_pos,
        }

        with torch.no_grad():
            obs_dict = {
                k: torch.from_numpy(v).unsqueeze(0).to(self.torch_device) for k, v in obs.items() if k in self.rgb_keys or k in self.lowdim_keys
            }
            batch = {"obs": obs_dict}
            if actions["instruction"] is not None:
                # 如果有指令，则将其添加到batch中
                self.logger.log_info(f"Using instruction: {actions['instruction']}")
                batch['instruction'] = actions["instruction"]
            elif self.instruction is not None:
                batch['instruction'] = [self.instruction]

            result = self.policy.predict_action(batch)
            action_pred = result["action_pred"][0].detach().to("cpu").numpy()
            
            if action_pred.shape[-1] == 20:
                print("convert to euler")
                action_chunk_size = action_pred.shape[0]
                action_pred_euler = np.zeros((action_chunk_size, 14))
                for i in range(action_chunk_size):
                    action_pred_euler[i, :3] = action_pred[i, :3]
                    action_pred_euler[i, 3:6] = convert_6D_to_euler(action_pred[i, 3:9])
                    action_pred_euler[i, 6] = action_pred[i, 9]
                    action_pred_euler[i, 7:10] = action_pred[i, 10:13]
                    action_pred_euler[i, 10:13] = convert_6D_to_euler(action_pred[i, 13:19])
                    action_pred_euler[i, 13] = action_pred[i, 19]
                action_pred = action_pred_euler
                # action_pred = relative_to_actions(action_pred, raw_agent_data[0])
            left_action_pred = action_pred[:, :7]
            right_action_pred = action_pred[:, 7:14]
            self.logger.log_info(f"left_action_pred: {left_action_pred.shape}, right_action_pred: {right_action_pred.shape}")

        if self.history_len > 0:
            for action in action_pred.tolist():
                self.arm_history.add_action(action)

        # DEBUG 打印FOLLOW1_POS、FOLLOW2_POS在[0, :]的值
        self.logger.log_info(f"[DEBUG BEFORE INTER] FOLLOW1_POS value: {left_action_pred[0, :]}, FOLLOW2_POS value: {right_action_pred[0, :]}")
        inter_len = self.action_interpolate_multiplier * len(left_action_pred)
        left_action_pred = interpolates_actions(
            actions=left_action_pred, num_actions=left_action_pred.shape[0], target_num_actions=inter_len, action_dim=7
        )
        right_action_pred = interpolates_actions(
            actions=right_action_pred, num_actions=right_action_pred.shape[0], target_num_actions=inter_len, action_dim=7
        )
        start_frame = int(self.action_start_ratio * inter_len)
        end_frame = int(self.action_end_ratio * inter_len)
        left_action_pred = left_action_pred[start_frame:end_frame]
        right_action_pred = right_action_pred[start_frame:end_frame]

        # 调整follow gripper
        follow1 = left_action_pred
        follow2 = right_action_pred
        self.logger.log_info(f"follow1: {follow1.shape}, follow2: {follow2.shape}")
        # follow1[:, -1] = 0.0
        # follow2[:, -1] = np.maximum(follow2[:, -1] - 0.0, 0.0)

        serialized_actions = {"follow1_pos": follow1, "follow2_pos": follow2, "follow1_joints": follow1, "follow2_joints": follow1}  # 7
        return serialized_actions

    def execute(self, requests):
        """处理推理请求，生成机械臂控制指令"""
        responses = []
        start_time = time.time()
        for request in requests:
            try:
                # 1. 从请求中提取输入张量
                inputs = {
                    "follow1_pos": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW1_POS").as_numpy(),
                    "follow2_pos": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW2_POS").as_numpy(),
                    "follow1_joints_cur": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW1_JOINTS_CUR").as_numpy(),
                    "follow2_joints_cur": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW2_JOINTS_CUR").as_numpy(),
                }
                self.logger.log_info(f"[DEBUG INPUT] follow1_pos value: {inputs['follow1_pos']}, follow2_pos value: {inputs['follow2_pos']}")

                instruction_tensor = pb_utils.get_input_tensor_by_name(request, "INSTRUCTION")
                if instruction_tensor is not None:
                    # 获取字节流数据并解码为字符串
                    instruction_bytes = instruction_tensor.as_numpy()[0]
                    instruction = instruction_bytes.decode('utf-8')
                    inputs["instruction"] = instruction
                    self.logger.log_info(f"[DEBUG INPUT] instruction: {instruction}")
                else:
                    inputs["instruction"] = None
                    self.logger.log_info("[DEBUG INPUT] instruction: None")

                # # 2. 处理可选摄像头输入
                views = {}
                for cam_name in ["CAMERA_LEFT", "CAMERA_FRONT", "CAMERA_RIGHT"]:
                    tensor = pb_utils.get_input_tensor_by_name(request, cam_name)
                    if not tensor:
                        continue  # 跳过不存在的可选输入
                        
                    # 2. 获取字节流数据（形状为[1]的BYTES类型）
                    byte_data_array = tensor.as_numpy()
                    if byte_data_array.size == 0:
                        continue
                        
                    # 3. 解压第一个元素（因为dims=[1]）
                    byte_data = byte_data_array[0]
                    try:
                        # 4. 解压缩图像并存储
                        img = decompress_image(byte_data)
                        if img is not None:
                            # 添加批次维度并转换为BGR->RGB（如果需要）
                            img = img[np.newaxis, ...]  # 变成 [1,H,W,C]
                            views[cam_name.lower()] = img
                    except Exception as e:
                        print(f"解压{cam_name}失败: {str(e)}")

                # 2. 处理可选摄像头输入
                # views = {}
                # for cam_name in ["CAMERA_LEFT", "CAMERA_FRONT", "CAMERA_RIGHT"]:
                #     tensor = pb_utils.get_input_tensor_by_name(request, cam_name)
                #     if tensor:
                #         img = tensor.as_numpy()
                #         views[cam_name.lower()] = img
                # 3. 执行预测
                with torch.no_grad():
                    actions = self._pred_func(views, inputs)

                # 4. 构建输出张量
                output_tensors = []
                for output_name in ["FOLLOW1_POS", "FOLLOW2_POS", "FOLLOW1_JOINTS", "FOLLOW2_JOINTS"]:
                    key = output_name.lower().replace("follow", "follow")
                    data = np.array(actions[key], dtype=np.float32)
                    tensor = pb_utils.Tensor(output_name, data)
                    output_tensors.append(tensor)
                # DEBUG 打印FOLLOW1_POS、FOLLOW2_POS在[0, :]的值
                self.logger.log_info(
                    f"[DEBUG OUTPUT] FOLLOW1_POS value: {actions['follow1_pos'][0, :]}, FOLLOW2_POS value: {actions['follow2_pos'][0, :]}"
                )

                # 5. 构造响应
                responses.append(pb_utils.InferenceResponse(output_tensors))

            except Exception as e:
                # 获取完整错误堆栈
                exc_type, exc_value, exc_tb = sys.exc_info()

                # 生成带行号的详细堆栈信息
                tb_lines = traceback.format_exception(exc_type, exc_value, exc_tb)
                full_traceback = "".join(tb_lines)

                # 截取最近5层堆栈（避免日志过长）
                relevant_stack = "\n".join([line.strip() for line in islice(full_traceback.split("\n"), 0, 15)])  # 保留前15行

                # 记录完整堆栈到服务端日志
                self.logger.log_error(f"完整堆栈:\n{full_traceback}")

                # 返回精简的客户端错误信息
                error_msg = f"推理失败: {str(e)}\n" f"最近调用堆栈:\n{str(relevant_stack)}"
                error = pb_utils.TritonError(error_msg)
                responses.append(pb_utils.InferenceResponse(error=error))
        output_duration = time.time() - start_time
        self.logger.log_info(f"Processed {len(requests)} requests, cost {output_duration:.2f} seconds.")
        return responses

    def finalize(self):
        """`finalize` is called only once when the model is being unloaded.
        Implementing `finalize` function is optional. This function allows
        the model to perform any necessary clean ups before exit.
        """
        print("Cleaning up...")
