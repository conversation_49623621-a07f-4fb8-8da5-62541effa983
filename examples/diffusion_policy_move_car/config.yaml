# config.yaml
name: starrick-test
image: nvcr.io/nvidia/tritonserver:25.03-py3-x2infer-1.03
namespace: deploy
backend_path: "/home/<USER>/workspace/x2robot_infer/examples/diffusion_policy/backend"
download_path: "dummy@**************:/x2robot/share/dp_ckpt/0407/take_cup/epoch=0100-train_loss=0.001.ckpt"
env_vars:
  ACTION_INTERPOLATE_MULTIPLIER: "28"
  ACTION_START_RATIO: "0.15"
  ACTION_END_RATIO: "0.8"
  NVIDIA_DRIVER_CAPABILITIES: "all"