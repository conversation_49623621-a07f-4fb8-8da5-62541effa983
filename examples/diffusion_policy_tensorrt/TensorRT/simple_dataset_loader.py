#!/usr/bin/env python3
"""
数据集加载器
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional
import traceback

# 添加项目根目录到路径
ROOT_DIR = str(Path(__file__).parent.parent)
sys.path.append(ROOT_DIR)

class SimpleDatasetLoader:
    """简化的数据集加载器"""
    
    def __init__(self):
        """初始化数据集加载器"""
        self.dataset = None
        self.val_dataloader = None
        self.normalizer = None  # 用于Ground Truth反归一化

        # 设置分布式环境
        self._setup_distributed()

        # 初始化数据集
        self._initialize_dataset()
    
    def _setup_distributed(self):
        """设置分布式环境（单进程模式）"""
        try:
            os.environ['MASTER_ADDR'] = 'localhost'
            os.environ['MASTER_PORT'] = '12355'
            print(f"✅ 分布式环境设置完成")
        except Exception as e:
            print(f"❌ 分布式环境设置失败: {e}")
            raise
    
    def _initialize_dataset(self):
        """初始化数据集，严格按照训练文件逻辑"""
        try:
            # 导入必要的模块
            from x2robot_dataset.dynamic_robot_dataset import DynamicRobotDataset
            from x2robot_dataset.lazy_dataset import X2RDataChunkConfig, X2RDataProcessingConfig
            from x2robot_dataset.common.data_preprocessing import _CAM_MAPPING
            
            print(f"🔄 正在初始化数据集...")
            
            # 数据集配置文件路径（直接使用任务配置文件）
            dataset_config_path = "/workspace/diffusion_policy_workshop/diffusion_policy/config/task/entangle_line_threefork.yaml"
            print(f"   数据集配置文件: {dataset_config_path}")
            
            # 使用训练文件中相同的相机映射
            cam_mapping = _CAM_MAPPING
            rgb_keys = ['face_view', 'left_wrist_view', 'right_wrist_view']
            
            # 过滤掉不在rgb_keys里的cam
            filter_cam_mapping = {}
            for key, value in cam_mapping.items():
                if value in rgb_keys:
                    filter_cam_mapping[key] = value
            cam_mapping = filter_cam_mapping
            
            print(f"   相机映射: {cam_mapping}")
            
            # 动作键配置（与训练文件一致）
            full_action_keys_needed = [
                "follow_left_ee_cartesian_pos",
                "follow_left_ee_rotation", 
                "follow_left_gripper",
                "follow_right_ee_cartesian_pos",
                "follow_right_ee_rotation",
                "follow_right_gripper"
            ]
            prediction_action_keys = full_action_keys_needed
            
            # 创建数据处理配置
            data_config = X2RDataProcessingConfig()
            data_config.update(
                cam_mapping=cam_mapping,
                class_type="x2",
                train_test_split=0.9,  # 训练验证分割
                filter_angle_outliers=False,
                sample_rate=1.0,
                parse_tactile=False,
                action_keys=full_action_keys_needed,
                predict_action_keys=prediction_action_keys,
                use_gripper_cur=False,
                trim_stationary=False
            )
            
            # 创建数据块配置
            action_horizon = 20
            action_history_length = 0
            data_chunk_config = X2RDataChunkConfig().update(
                left_padding=True if action_history_length > 0 else False,
                right_padding=True,
                action_horizon=action_horizon + 1,
                action_history_length=action_history_length,
            )
            
            print(f"   数据配置创建完成")
            
            # 创建数据集实例（使用极小的buffer_size快速获取数据）
            self.dataset = DynamicRobotDataset(
                dataset_config_path=dataset_config_path,
                data_config=data_config,
                data_chunk_config=data_chunk_config,
                rank=0,  # 单进程模式，对应accelerator.process_index
                world_size=1,  # 单进程模式，对应accelerator.num_processes
                batch_size=1,  # 测试时使用batch_size=1
                buffer_size=2,  # 使用极小的buffer_size，只需要2个样本就能产生batch
            )
            
            # 获取验证数据加载器
            self.val_dataloader = self.dataset.get_val_dataloader()
            
            print(f"✅ 数据集初始化成功")
            print(f"   验证数据加载器创建完成")

            # 检查数据集状态
            self._check_dataset_status()

            # 模拟训练文件中的 accelerator.wait_for_everyone()
            print(f"   等待数据集完全初始化...")
            import time
            time.sleep(5)  # 给数据处理线程一些时间启动

            # 等待数据处理完成
            self._wait_for_data_ready()

        except Exception as e:
            print(f"❌ 数据集初始化失败: {e}")
            traceback.print_exc()
            raise

    def _wait_for_data_ready(self, max_wait_time: int = 600):
        """等待数据处理完成，更长的等待时间"""
        try:
            print(f"⏳ 等待数据处理完成（最多等待{max_wait_time}秒）...")
            print(f"   注意：首次运行需要处理视频数据，可能需要较长时间")

            import time
            start_time = time.time()
            last_print_time = 0

            while time.time() - start_time < max_wait_time:
                # 检查batch队列是否有数据
                if hasattr(self.dataset, 'val_batch_queue'):
                    queue_size = self.dataset.val_batch_queue.qsize()
                    if queue_size > 0:
                        print(f"✅ 数据处理完成！val_batch_queue有 {queue_size} 个批次")
                        return True

                # 检查缓冲队列是否有数据
                if hasattr(self.val_dataloader, 'buffer_queue'):
                    buffer_queue_size = self.val_dataloader.buffer_queue.qsize()
                    if buffer_queue_size > 0:
                        print(f"✅ 数据处理完成！buffer_queue有 {buffer_queue_size} 个批次")
                        return True

                # 每30秒打印一次状态
                elapsed = time.time() - start_time
                if elapsed - last_print_time >= 30:
                    print(f"   等待中... 已等待 {elapsed:.0f}秒")
                    last_print_time = elapsed

                time.sleep(1)

            print(f"⚠️ 等待数据处理超时（{max_wait_time}秒），但继续尝试...")
            return False

        except Exception as e:
            print(f"❌ 等待数据处理时出错: {e}")
            return False

    def _check_dataset_status(self):
        """检查数据集状态"""
        try:
            print(f"📊 检查数据集状态:")

            # 检查全局迭代器
            if hasattr(self.dataset, 'global_train_iters'):
                train_iters = self.dataset.global_train_iters.value
                print(f"   训练迭代数: {train_iters}")

            if hasattr(self.dataset, 'global_val_iters'):
                val_iters = self.dataset.global_val_iters.value
                print(f"   验证迭代数: {val_iters}")

            # 检查数据加载器状态
            if hasattr(self.val_dataloader, 'iter_count'):
                print(f"   当前迭代计数: {self.val_dataloader.iter_count}")

            # 检查缓冲队列状态
            if hasattr(self.val_dataloader, 'buffer_queue'):
                queue_size = self.val_dataloader.buffer_queue.qsize()
                print(f"   缓冲队列大小: {queue_size}")

        except Exception as e:
            print(f"   ⚠️ 检查数据集状态时出错: {e}")
    
    def get_sample_batch(self, timeout_seconds: int = 60) -> Dict:
        """获取单个样本批次，带超时机制"""
        try:
            print(f"   正在获取数据批次...")
            return self.get_single_batch_with_retry(max_retries=3, timeout_per_try=timeout_seconds//3)

        except Exception as e:
            print(f"❌ 获取样本批次失败: {e}")
            traceback.print_exc()
            raise
    
    def get_test_iterator(self, max_samples: int = 10, timeout_total: int = 120):
        """获取测试数据迭代器，按照训练文件的方式"""
        try:
            if self.val_dataloader is None:
                raise ValueError("验证数据加载器未初始化")

            print(f"   开始获取数据迭代器，最多{max_samples}个样本，总超时{timeout_total}秒")

            # 使用总超时机制
            import signal

            def timeout_handler(signum, frame):
                raise TimeoutError(f"获取数据迭代器总超时（{timeout_total}秒）")

            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(timeout_total)

            try:
                count = 0
                # 按照训练文件的方式：for batch_idx, batch in enumerate(val_dataloader)
                for batch_idx, batch in enumerate(self.val_dataloader):
                    if count >= max_samples:
                        break

                    print(f"   ✅ 成功获取第{count+1}个样本 (batch_idx: {batch_idx})")

                    # 移动到CUDA设备
                    batch = self._move_to_device(batch, 'cuda')

                    yield batch
                    count += 1

                signal.alarm(0)  # 取消超时
                print(f"   ℹ️ 数据迭代器完成，共获取{count}个样本")

            except TimeoutError:
                signal.alarm(0)
                print(f"   ❌ 数据迭代器获取超时")
                raise

        except Exception as e:
            print(f"❌ 创建测试迭代器失败: {e}")
            traceback.print_exc()
            raise

    def get_batch_from_queue_directly(self, timeout_seconds: int = 60):
        """直接从batch_queue获取数据"""
        try:
            print(f"   尝试直接从batch_queue获取数据（超时: {timeout_seconds}秒）")

            # 直接访问数据集的val_batch_queue
            if hasattr(self.dataset, 'val_batch_queue'):
                import signal

                def timeout_handler(signum, frame):
                    raise TimeoutError(f"从batch_queue获取数据超时（{timeout_seconds}秒）")

                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(timeout_seconds)

                try:
                    # 直接从队列获取批次
                    batch = self.dataset.val_batch_queue.get(block=True, timeout=timeout_seconds)
                    signal.alarm(0)  # 取消超时

                    print(f"   ✅ 成功从batch_queue获取数据批次")

                    # 移动到CUDA设备
                    batch = self._move_to_device(batch, 'cuda')

                    return batch

                except TimeoutError:
                    signal.alarm(0)
                    print(f"   ❌ 从batch_queue获取数据超时")
                    raise

            else:
                raise ValueError("数据集没有val_batch_queue属性")

        except Exception as e:
            print(f"❌ 直接从batch_queue获取数据失败: {e}")
            traceback.print_exc()
            raise

    def get_single_batch_with_retry(self, max_retries: int = 3, timeout_per_try: int = 30, skip_batches: int = 0):
        """获取单个批次数据，尝试多种方法

        Args:
            max_retries: 最大重试次数
            timeout_per_try: 每次尝试的超时时间
            skip_batches: 跳过的批次数量，用于获取不同的样本
        """
        try:
            if self.val_dataloader is None:
                raise ValueError("验证数据加载器未初始化")

            for attempt in range(max_retries):
                try:
                    print(f"   尝试获取数据批次 (第{attempt+1}/{max_retries}次尝试)")

                    # 方法1：尝试直接从batch_queue获取
                    if attempt == 0:
                        try:
                            return self.get_batch_from_queue_directly(timeout_per_try)
                        except Exception as e:
                            print(f"   方法1失败: {e}")

                    # 方法2：按照训练文件的方式，直接遍历dataloader
                    import signal

                    def timeout_handler(signum, frame):
                        raise TimeoutError(f"获取数据批次超时（{timeout_per_try}秒）")

                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(timeout_per_try)

                    try:
                        # 按照训练文件的方式：for batch_idx, batch in enumerate(val_dataloader)
                        current_batch_count = 0
                        for batch_idx, batch in enumerate(self.val_dataloader):
                            signal.alarm(0)  # 取消超时

                            # 跳过指定数量的批次
                            if current_batch_count < skip_batches:
                                current_batch_count += 1
                                print(f"   跳过批次 {current_batch_count}/{skip_batches}")
                                continue

                            print(f"   ✅ 成功获取数据批次 (batch_idx: {batch_idx}, 跳过了 {skip_batches} 个批次)")

                            # 移动到CUDA设备
                            batch = self._move_to_device(batch, 'cuda')

                            return batch  # 获取目标批次后返回

                        # 如果循环结束都没有数据
                        signal.alarm(0)
                        raise RuntimeError("数据加载器为空，没有可用的批次")

                    except TimeoutError:
                        signal.alarm(0)
                        print(f"   ❌ 第{attempt+1}次尝试超时")
                        if attempt == max_retries - 1:
                            raise TimeoutError(f"所有{max_retries}次尝试都超时")
                        continue

                except Exception as e:
                    print(f"   ❌ 第{attempt+1}次尝试失败: {e}")
                    if attempt == max_retries - 1:
                        raise
                    continue

            raise RuntimeError(f"所有{max_retries}次尝试都失败")

        except Exception as e:
            print(f"❌ 获取单个批次失败: {e}")
            traceback.print_exc()
            raise
    
    def _move_to_device(self, batch: Dict, device: str) -> Dict:
        """将批次数据移动到指定设备"""
        def move_tensor(x):
            if isinstance(x, torch.Tensor):
                return x.to(device)
            return x
        
        return self._apply_to_dict(batch, move_tensor)
    
    def _apply_to_dict(self, d: Dict, func) -> Dict:
        """递归地对字典中的所有值应用函数"""
        result = {}
        for key, value in d.items():
            if isinstance(value, dict):
                result[key] = self._apply_to_dict(value, func)
            else:
                result[key] = func(value)
        return result
    
    def prepare_obs_for_inference(self, batch: Dict) -> Dict:
        """为推理准备观测数据"""
        try:
            if 'obs' not in batch:
                raise ValueError("批次数据中缺少 'obs' 键")
            
            obs_data = batch['obs']
            
            # 验证必要的键是否存在
            required_keys = ['face_view', 'left_wrist_view', 'right_wrist_view', 'agent_pos']
            for key in required_keys:
                if key not in obs_data:
                    raise ValueError(f"观测数据中缺少必要的键: {key}")
            
            # 构建推理用的观测字典
            obs_dict = {'obs': obs_data}
            
            return obs_dict
            
        except Exception as e:
            print(f"❌ 准备推理观测数据失败: {e}")
            traceback.print_exc()
            raise
    
    def get_ground_truth_action(self, batch: Dict, unnormalize: bool = True) -> torch.Tensor:
        """获取真实动作标签

        Args:
            batch: 数据批次
            unnormalize: 是否进行反归一化处理（默认True，与PyTorch模型预测保持一致）
        """
        try:
            if 'action' not in batch:
                raise ValueError("批次数据中缺少 'action' 键")

            action = batch['action']

            if unnormalize and hasattr(self, 'normalizer') and self.normalizer is not None:
                # 进行反归一化，与PyTorch模型预测保持一致的尺度
                try:
                    action = self.normalizer['action'].unnormalize(action)
                    print(f"   ✅ Ground Truth已反归一化到原始尺度")
                except Exception as e:
                    print(f"   ⚠️ Ground Truth反归一化失败，使用原始数据: {e}")
            else:
                print(f"   ⚠️ 未进行Ground Truth反归一化（normalizer不可用）")

            return action

        except Exception as e:
            print(f"❌ 获取真实动作失败: {e}")
            traceback.print_exc()
            raise
    
    def print_sample_info(self, batch: Dict):
        """打印样本信息"""
        print(f"📊 样本信息:")

        if 'obs' in batch:
            print(f"   观测数据:")
            for key, value in batch['obs'].items():
                if isinstance(value, torch.Tensor):
                    print(f"     {key}: shape={value.shape}, dtype={value.dtype}, device={value.device}")
                    print(f"       min={torch.min(value).item():.6f}, max={torch.max(value).item():.6f}")
                    print(f"       mean={torch.mean(value.float()).item():.6f}, std={torch.std(value.float()).item():.6f}")

        if 'action' in batch:
            action = batch['action']
            if isinstance(action, torch.Tensor):
                print(f"   动作数据:")
                print(f"     action: shape={action.shape}, dtype={action.dtype}, device={action.device}")
                print(f"       min={torch.min(action).item():.6f}, max={torch.max(action).item():.6f}")
                print(f"       mean={torch.mean(action).item():.6f}, std={torch.std(action).item():.6f}")



    def test_data_access(self, max_samples: int = 5):
        """测试数据访问功能"""
        print(f"\n🔍 测试数据访问功能 (最多{max_samples}个样本)")
        print(f"="*60)

        try:
            success_count = 0
            error_count = 0

            for i, batch in enumerate(self.get_test_iterator(max_samples=max_samples)):
                print(f"\n📦 样本 {i+1}:")

                try:
                    # 检查批次结构
                    if not isinstance(batch, dict):
                        print(f"   ❌ 批次不是字典类型: {type(batch)}")
                        error_count += 1
                        continue

                    print(f"   批次键: {list(batch.keys())}")

                    # 检查观测数据
                    if 'obs' in batch:
                        obs = batch['obs']
                        print(f"   观测键: {list(obs.keys())}")

                        # 检查必要的观测键
                        required_obs_keys = ['face_view', 'left_wrist_view', 'right_wrist_view', 'agent_pos']
                        missing_keys = []
                        for key in required_obs_keys:
                            if key not in obs:
                                missing_keys.append(key)

                        if missing_keys:
                            print(f"   ❌ 缺少观测键: {missing_keys}")
                            error_count += 1
                            continue
                        else:
                            print(f"   ✅ 所有必要观测键都存在")

                        # 检查观测数据形状和类型
                        for key, value in obs.items():
                            if isinstance(value, torch.Tensor):
                                print(f"     {key}: {value.shape} {value.dtype}")

                                # 检查数据是否有效（不是全零或全NaN）
                                if torch.all(value == 0):
                                    print(f"       ⚠️ {key} 数据全为零")
                                elif torch.any(torch.isnan(value)):
                                    print(f"       ❌ {key} 数据包含NaN")
                                    error_count += 1
                                    continue
                                else:
                                    print(f"       ✅ {key} 数据有效")
                    else:
                        print(f"   ❌ 批次中缺少 'obs' 键")
                        error_count += 1
                        continue

                    # 检查动作数据
                    if 'action' in batch:
                        action = batch['action']
                        if isinstance(action, torch.Tensor):
                            print(f"   动作形状: {action.shape} {action.dtype}")

                            # 检查动作数据是否有效
                            if torch.all(action == 0):
                                print(f"     ⚠️ 动作数据全为零")
                            elif torch.any(torch.isnan(action)):
                                print(f"     ❌ 动作数据包含NaN")
                                error_count += 1
                                continue
                            else:
                                print(f"     ✅ 动作数据有效")
                        else:
                            print(f"   ❌ 动作数据不是张量: {type(action)}")
                            error_count += 1
                            continue
                    else:
                        print(f"   ❌ 批次中缺少 'action' 键")
                        error_count += 1
                        continue

                    # 测试推理数据准备
                    try:
                        obs_dict = self.prepare_obs_for_inference(batch)
                        ground_truth = self.get_ground_truth_action(batch)
                        print(f"   ✅ 推理数据准备成功")
                        print(f"     obs_dict keys: {list(obs_dict['obs'].keys())}")
                        print(f"     ground_truth shape: {ground_truth.shape}")
                    except Exception as e:
                        print(f"   ❌ 推理数据准备失败: {e}")
                        error_count += 1
                        continue

                    success_count += 1
                    print(f"   ✅ 样本 {i+1} 访问成功")

                except Exception as e:
                    print(f"   ❌ 样本 {i+1} 访问失败: {e}")
                    error_count += 1
                    continue

            print(f"\n" + "="*60)
            print(f"📊 数据访问测试结果:")
            print(f"   总样本数: {success_count + error_count}")
            print(f"   成功样本数: {success_count}")
            print(f"   失败样本数: {error_count}")
            print(f"   成功率: {success_count/(success_count + error_count)*100:.1f}%" if (success_count + error_count) > 0 else "0.0%")

            if success_count > 0:
                print(f"✅ 数据访问测试通过！数据集可以正常访问")
                return True
            else:
                print(f"❌ 数据访问测试失败！所有样本都无法访问")
                return False

        except Exception as e:
            print(f"❌ 数据访问测试过程中出错: {e}")
            traceback.print_exc()
            return False

    def test_dataset_consistency(self, num_samples: int = 3):
        """测试数据集一致性"""
        print(f"\n🔍 测试数据集一致性 ({num_samples}个样本)")
        print(f"="*60)

        try:
            shapes_record = {}
            dtypes_record = {}

            for i, batch in enumerate(self.get_test_iterator(max_samples=num_samples)):
                print(f"\n📦 样本 {i+1} 一致性检查:")

                # 记录观测数据的形状和类型
                if 'obs' in batch:
                    for key, value in batch['obs'].items():
                        if isinstance(value, torch.Tensor):
                            current_shape = tuple(value.shape)
                            current_dtype = value.dtype

                            if key not in shapes_record:
                                shapes_record[key] = current_shape
                                dtypes_record[key] = current_dtype
                                print(f"   {key}: 初始形状 {current_shape}, 类型 {current_dtype}")
                            else:
                                if shapes_record[key] != current_shape:
                                    print(f"   ❌ {key} 形状不一致: 期望 {shapes_record[key]}, 实际 {current_shape}")
                                    return False
                                if dtypes_record[key] != current_dtype:
                                    print(f"   ❌ {key} 类型不一致: 期望 {dtypes_record[key]}, 实际 {current_dtype}")
                                    return False
                                print(f"   ✅ {key}: 形状和类型一致")

                # 记录动作数据的形状和类型
                if 'action' in batch:
                    action = batch['action']
                    if isinstance(action, torch.Tensor):
                        current_shape = tuple(action.shape)
                        current_dtype = action.dtype

                        if 'action' not in shapes_record:
                            shapes_record['action'] = current_shape
                            dtypes_record['action'] = current_dtype
                            print(f"   action: 初始形状 {current_shape}, 类型 {current_dtype}")
                        else:
                            if shapes_record['action'] != current_shape:
                                print(f"   ❌ action 形状不一致: 期望 {shapes_record['action']}, 实际 {current_shape}")
                                return False
                            if dtypes_record['action'] != current_dtype:
                                print(f"   ❌ action 类型不一致: 期望 {dtypes_record['action']}, 实际 {current_dtype}")
                                return False
                            print(f"   ✅ action: 形状和类型一致")

            print(f"\n✅ 数据集一致性测试通过！所有样本的形状和类型都一致")
            return True

        except Exception as e:
            print(f"❌ 数据集一致性测试失败: {e}")
            traceback.print_exc()
            return False

def test_simple_dataset_loader():
    """测试简化数据集加载器"""
    print(f"🧪 测试简化数据集加载器")

    try:
        # 创建数据集加载器
        loader = SimpleDatasetLoader()

        # 1. 测试数据访问功能
        print(f"\n" + "="*80)
        print(f"第一步：测试数据访问功能")
        access_success = loader.test_data_access(max_samples=3)

        if not access_success:
            print(f"❌ 数据访问测试失败，停止后续测试")
            return False

        # 2. 测试数据集一致性
        print(f"\n" + "="*80)
        print(f"第二步：测试数据集一致性")
        consistency_success = loader.test_dataset_consistency(num_samples=3)

        if not consistency_success:
            print(f"❌ 数据集一致性测试失败，停止后续测试")
            return False

        # 3. 获取单个样本进行详细检查
        print(f"\n" + "="*80)
        print(f"第三步：获取单个样本进行详细检查")
        try:
            batch = loader.get_sample_batch(timeout_seconds=30)
            loader.print_sample_info(batch)

            # 测试推理数据准备
            obs_dict = loader.prepare_obs_for_inference(batch)
            ground_truth = loader.get_ground_truth_action(batch)

            print(f"\n✅ 推理数据准备成功")
            print(f"   obs_dict keys: {list(obs_dict['obs'].keys())}")
            print(f"   ground_truth shape: {ground_truth.shape}")

        except Exception as e:
            print(f"❌ 单个样本测试失败: {e}")
            return False

        print(f"\n" + "="*80)
        print(f"🎉 所有测试通过！数据集可以正常使用")
        print(f"✅ 数据访问测试: 通过")
        print(f"✅ 数据一致性测试: 通过")
        print(f"✅ 推理数据准备: 通过")

        return True

    except Exception as e:
        print(f"❌ 简化数据集加载器测试失败: {e}")
        traceback.print_exc()
        return False

def test_dataset_episodes():
    """测试检查数据集episodes内容（不等待数据处理）"""
    print(f"🧪 检查数据集episodes内容")

    try:
        # 直接创建数据集，不通过SimpleDatasetLoader
        from x2robot_dataset.dynamic_robot_dataset import DynamicRobotDataset
        from x2robot_dataset.lazy_dataset import X2RDataChunkConfig, X2RDataProcessingConfig
        from x2robot_dataset.common.data_preprocessing import _CAM_MAPPING

        print(f"🔄 直接初始化数据集（跳过数据处理）...")

        # 设置分布式环境变量
        import os
        os.environ['MASTER_ADDR'] = 'localhost'
        os.environ['MASTER_PORT'] = '12355'

        # 数据集配置
        dataset_config_path = "/workspace/diffusion_policy_workshop/diffusion_policy/config/task/entangle_line_threefork.yaml"

        # 相机映射
        cam_mapping = _CAM_MAPPING
        rgb_keys = ['face_view', 'left_wrist_view', 'right_wrist_view']
        filter_cam_mapping = {}
        for key, value in cam_mapping.items():
            if value in rgb_keys:
                filter_cam_mapping[key] = value
        cam_mapping = filter_cam_mapping

        # 动作键配置
        full_action_keys_needed = [
            "follow_left_ee_cartesian_pos",
            "follow_left_ee_rotation",
            "follow_left_gripper",
            "follow_right_ee_cartesian_pos",
            "follow_right_ee_rotation",
            "follow_right_gripper"
        ]

        # 数据配置
        data_config = X2RDataProcessingConfig()
        data_config.update(
            cam_mapping=cam_mapping,
            class_type="x2",
            train_test_split=0.9,
            filter_angle_outliers=False,
            sample_rate=1.0,
            parse_tactile=False,
            action_keys=full_action_keys_needed,
            predict_action_keys=full_action_keys_needed,
            use_gripper_cur=False,
            trim_stationary=False
        )

        # 数据块配置
        data_chunk_config = X2RDataChunkConfig().update(
            left_padding=False,
            right_padding=True,
            action_horizon=21,
            action_history_length=0,
        )

        # 创建数据集（不启动数据处理）
        dataset = DynamicRobotDataset(
            dataset_config_path=dataset_config_path,
            data_config=data_config,
            data_chunk_config=data_chunk_config,
            rank=0,
            world_size=1,
            batch_size=1,
        )

        # 等待episodes加载完成
        import time
        max_wait = 60
        start_time = time.time()

        while time.time() - start_time < max_wait:
            if hasattr(dataset, 'val_episodes') and len(dataset.val_episodes) > 0:
                print(f"   找到 {len(dataset.val_episodes)} 个验证episodes")
                break
            print(f"   等待episodes加载... {time.time() - start_time:.1f}秒")
            time.sleep(2)

        print(f"\n📊 数据集状态:")
        if hasattr(dataset, 'global_val_iters'):
            val_iters = dataset.global_val_iters.value
            print(f"   验证迭代数: {val_iters}")

        # 检查数据集的所有属性
        print(f"\n🔍 数据集属性:")
        dataset_attrs = [attr for attr in dir(dataset) if not attr.startswith('_')]
        for attr in dataset_attrs:
            if 'episode' in attr.lower():
                try:
                    value = getattr(dataset, attr)
                    if hasattr(value, '__len__'):
                        print(f"   {attr}: {type(value)} (长度: {len(value)})")
                    else:
                        print(f"   {attr}: {type(value)}")
                except:
                    print(f"   {attr}: 无法访问")

        # 尝试不同的episode属性名
        episode_attrs = ['val_episodes', 'validation_episodes', 'episodes', 'val_data', 'validation_data']
        found_episodes = None

        for attr_name in episode_attrs:
            if hasattr(dataset, attr_name):
                try:
                    episodes = getattr(dataset, attr_name)
                    if hasattr(episodes, '__len__') and len(episodes) > 0:
                        print(f"\n✅ 找到episodes: {attr_name} (数量: {len(episodes)})")
                        found_episodes = episodes
                        break
                except:
                    continue

        if found_episodes and len(found_episodes) > 0:
            print(f"\n📋 第一个episode信息:")
            first_episode = found_episodes[0]
            for key, value in first_episode.items():
                if key == 'path':
                    print(f"     {key}: {value}")
                elif key in ['st_frame', 'ed_frame', 'num_frames']:
                    print(f"     {key}: {value}")
                else:
                    print(f"     {key}: {type(value)}")

            # 检查数据文件是否存在
            episode_path = first_episode.get('path', '')
            if episode_path:
                import os
                print(f"\n📁 检查episode路径:")
                print(f"     路径存在: {os.path.exists(episode_path)}")
                if os.path.exists(episode_path):
                    files = os.listdir(episode_path)
                    video_files = [f for f in files if f.endswith(('.mp4', '.avi', '.mov'))]
                    print(f"     视频文件数量: {len(video_files)}")
                    if video_files:
                        print(f"     视频文件: {video_files[:3]}...")  # 只显示前3个
        else:
            print(f"\n❌ 未找到episodes数据")

        print(f"\n✅ 数据集episodes检查完成")
        return True

    except Exception as e:
        print(f"❌ 数据集episodes检查失败: {e}")
        traceback.print_exc()
        return False

def test_get_actual_data():
    """测试获取实际数据"""
    print(f"🧪 测试获取实际数据")

    try:
        # 先检查episodes
        if not test_dataset_episodes():
            return False

        # 创建数据集加载器
        loader = SimpleDatasetLoader()

        print(f"\n📊 数据集状态:")
        if hasattr(loader.dataset, 'global_val_iters'):
            val_iters = loader.dataset.global_val_iters.value
            print(f"   验证迭代数: {val_iters}")

        # 尝试获取单个批次数据
        print(f"\n🔍 尝试获取单个批次数据:")
        batch = loader.get_single_batch_with_retry(max_retries=2, timeout_per_try=45)

        print(f"\n✅ 成功获取数据批次！")

        # 检查数据结构
        print(f"📊 数据批次信息:")
        print(f"   批次类型: {type(batch)}")
        print(f"   批次键: {list(batch.keys())}")

        if 'obs' in batch:
            obs = batch['obs']
            print(f"   观测键: {list(obs.keys())}")
            for key, value in obs.items():
                if isinstance(value, torch.Tensor):
                    print(f"     {key}: shape={value.shape}, dtype={value.dtype}, device={value.device}")

        if 'action' in batch:
            action = batch['action']
            if isinstance(action, torch.Tensor):
                print(f"   动作: shape={action.shape}, dtype={action.dtype}, device={action.device}")

        # 测试推理数据准备
        print(f"\n🔧 测试推理数据准备:")
        obs_dict = loader.prepare_obs_for_inference(batch)
        ground_truth = loader.get_ground_truth_action(batch)

        print(f"   ✅ 推理数据准备成功")
        print(f"   obs_dict keys: {list(obs_dict['obs'].keys())}")
        print(f"   ground_truth shape: {ground_truth.shape}")



        print(f"\n🎉 实际数据获取测试完全成功！")
        return True

    except Exception as e:
        print(f"❌ 获取实际数据测试失败: {e}")
        traceback.print_exc()
        return False

def test_dataset_status_only():
    """仅测试数据集状态（不获取实际数据）"""
    print(f"🧪 测试数据集状态")

    try:
        # 创建数据集加载器
        loader = SimpleDatasetLoader()

        print(f"\n📊 数据集加载完成，状态信息:")
        print(f"   数据集对象: {type(loader.dataset)}")
        print(f"   验证数据加载器: {type(loader.val_dataloader)}")

        # 检查数据集属性
        if hasattr(loader.dataset, 'global_train_iters'):
            train_iters = loader.dataset.global_train_iters.value
            print(f"   训练迭代数: {train_iters}")

        if hasattr(loader.dataset, 'global_val_iters'):
            val_iters = loader.dataset.global_val_iters.value
            print(f"   验证迭代数: {val_iters}")

            if val_iters > 0:
                print(f"✅ 数据集加载成功！验证集包含 {val_iters} 个批次")
                return True
            else:
                print(f"❌ 验证集为空")
                return False
        else:
            print(f"⚠️ 无法获取验证迭代数信息")
            return False

    except Exception as e:
        print(f"❌ 数据集状态测试失败: {e}")
        traceback.print_exc()
        return False



def test_dataset_access_only():
    """仅测试数据集访问功能（不进行推理测试）"""
    print(f"🧪 测试数据集访问功能")

    try:
        # 创建数据集加载器
        loader = SimpleDatasetLoader()

        # 测试数据访问
        success = loader.test_data_access(max_samples=5)

        if success:
            print(f"🎉 数据集访问测试成功！")
        else:
            print(f"❌ 数据集访问测试失败！")

        return success

    except Exception as e:
        print(f"❌ 数据集访问测试过程中出错: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="测试数据集加载器")
    parser.add_argument("--status-only", action="store_true",
                       help="仅检查数据集状态，不获取实际数据")
    parser.add_argument("--episodes", action="store_true",
                       help="检查数据集episodes内容")
    parser.add_argument("--get-data", action="store_true",
                       help="测试获取实际数据")

    parser.add_argument("--access-only", action="store_true",
                       help="仅测试数据访问功能，不进行完整测试")

    args = parser.parse_args()

    if args.status_only:
        print(f"🚀 运行数据集状态检查模式")
        success = test_dataset_status_only()
    elif args.episodes:
        print(f"🚀 运行episodes检查模式")
        success = test_dataset_episodes()
    elif args.get_data:
        print(f"🚀 运行实际数据获取测试模式")
        success = test_get_actual_data()

    elif args.access_only:
        print(f"🚀 运行数据访问测试模式")
        success = test_dataset_access_only()
    else:
        print(f"🚀 运行完整测试模式")
        success = test_simple_dataset_loader()

    sys.exit(0 if success else 1)
