#!/usr/bin/env python3
"""
ONNX导出工具
- 导出包含完整预处理流程的obs_encoder ONNX模型
- 导出UNet ONNX模型
- 验证ONNX与PyTorch模型的一致性
"""

import os
import sys
import torch
import numpy as np
import onnxruntime as ort
from pathlib import Path
from typing import Dict, Any, Tuple, Optional
import traceback

# 添加项目根目录到路径
ROOT_DIR = str(Path(__file__).parent.parent)
sys.path.append(ROOT_DIR)

class CorrectONNXExporter:
    """正确的ONNX导出器"""
    
    def __init__(self, 
                 checkpoint_path: str,
                 output_dir: str = "correct_onnx_models",
                 device: str = 'cuda'):
        """
        初始化导出器
        
        Args:
            checkpoint_path: 检查点文件路径
            output_dir: 输出目录
            device: 运行设备
        """
        self.checkpoint_path = checkpoint_path
        self.output_dir = output_dir
        self.device = device
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 加载PyTorch模型
        self.pytorch_model = None
        self._load_pytorch_model()
        
    def _load_pytorch_model(self):
        """加载PyTorch模型"""
        print(f"=== 加载PyTorch模型 ===")
        
        try:
            # 使用验证过的加载方式，但需要处理语言模型问题
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from test_tensorrt_pipeline import create_dummy_policy

            # 新checkpoint可能包含语言模型，使用智能加载方式
            try:
                self.pytorch_model = create_dummy_policy(self.checkpoint_path)
                if self.pytorch_model is None:
                    print(f"⚠️ create_dummy_policy返回None，可能是语言模型问题，尝试跳过...")
                    self.pytorch_model = self._create_policy_without_lm()
            except Exception as e:
                if "distilbert" in str(e).lower() or "HFValidationError" in str(e) or "lm_encoder" in str(e):
                    print(f"⚠️ 检测到语言模型配置问题，跳过语言模型...")
                    print(f"   错误信息: {str(e)[:200]}...")
                    self.pytorch_model = self._create_policy_without_lm()
                else:
                    raise e

            # 原始加载方式（备用，如果上面的智能加载失败）
            # self.pytorch_model = create_dummy_policy(self.checkpoint_path)

            if self.pytorch_model is None:
                raise RuntimeError("无法加载PyTorch模型")

            self.pytorch_model = self.pytorch_model.to(self.device).eval()
            
            print(f"✅ PyTorch模型加载成功")
            print(f"   设备: {self.pytorch_model.device}")
            print(f"   Normalizer keys: {list(self.pytorch_model.normalizer.params_dict.keys()) if self.pytorch_model.normalizer else 'None'}")
            print(f"   RGB keys: {self.pytorch_model.rgb_keys}")
            
        except Exception as e:
            print(f"❌ PyTorch模型加载失败: {e}")
            traceback.print_exc()
            raise

    def _create_policy_without_lm(self):
        """创建不包含语言模型的policy"""
        print(f"🔧 创建不包含语言模型的policy...")

        try:
            import torch
            import hydra
            from omegaconf import OmegaConf

            # 加载checkpoint
            from omegaconf import DictConfig
            torch.serialization.add_safe_globals([DictConfig])
            checkpoint = torch.load(self.checkpoint_path, map_location='cpu', weights_only=False)

            if 'cfg' not in checkpoint:
                raise ValueError("Checkpoint中没有找到cfg")

            cfg = checkpoint['cfg']

            # 修改配置，移除语言模型
            if hasattr(cfg.policy, 'lm_encoder'):
                print(f"   移除语言模型配置...")
                # 创建新的配置，不包含lm_encoder
                policy_cfg = OmegaConf.create(cfg.policy)
                if 'lm_encoder' in policy_cfg:
                    del policy_cfg['lm_encoder']

                # 如果obs_encoder中有language相关配置，也移除
                if hasattr(policy_cfg, 'obs_encoder') and hasattr(policy_cfg.obs_encoder, 'language_dim'):
                    policy_cfg.obs_encoder.language_dim = 0

                # 实例化policy
                policy = hydra.utils.instantiate(policy_cfg)

                # 加载权重
                if 'state_dicts' in checkpoint and 'model' in checkpoint['state_dicts']:
                    state_dict = checkpoint['state_dicts']['model']

                    # 过滤掉语言模型相关的权重
                    filtered_state_dict = {}
                    for key, value in state_dict.items():
                        if not key.startswith('lm_encoder'):
                            filtered_state_dict[key] = value

                    # 加载过滤后的权重
                    missing_keys, unexpected_keys = policy.load_state_dict(filtered_state_dict, strict=False)
                    print(f"   加载权重完成，忽略的键: {len([k for k in missing_keys if 'lm_encoder' in k])}")

                return policy
            else:
                # 如果没有语言模型，直接使用原始方法
                from test_tensorrt_pipeline import create_dummy_policy
                return create_dummy_policy(self.checkpoint_path)

        except Exception as e:
            print(f"❌ 创建无语言模型policy失败: {e}")
            traceback.print_exc()
            return None
    
    def _create_obs_encoder_with_normalizer(self):
        """创建包含normalizer的obs_encoder包装器"""
        print(f"\n=== 创建obs_encoder包装器 ===")
        
        class ObsEncoderWithNormalizer(torch.nn.Module):
            """包含完整预处理流程的obs_encoder（ONNX兼容版本）"""

            def __init__(self, model):
                super().__init__()
                self.normalizer = model.normalizer
                self.obs_encoder = model.obs_encoder
                self.rgb_keys = model.rgb_keys

                # 创建ONNX兼容的图像预处理
                self._create_onnx_compatible_transforms()

            def _create_onnx_compatible_transforms(self):
                """创建ONNX兼容的图像变换"""
                import torch.nn.functional as F

                class ONNXCompatibleImageProcessor(torch.nn.Module):
                    def __init__(self):
                        super().__init__()

                    def forward(self, img):
                        # img shape: (B, T, C, H, W)
                        B, T, C, H, W = img.shape

                        # 由于当前配置是resize_shape=[480, 640], crop_shape=[480, 640]
                        # 实际上不需要crop和resize，只需要除以255

                        # 除以255并clamp到[0,1]
                        img = img / 255.0
                        img = torch.clamp(img, 0.0, 1.0)

                        return img

                self.image_processor = ONNXCompatibleImageProcessor()

            def _manual_obs_encoding(self, obs_dict):
                """手动实现obs_encoder功能，避免transform问题"""
                # 处理RGB输入
                rgb_features = []
                for key in self.rgb_keys:
                    if key in obs_dict:
                        img = obs_dict[key]  # shape: (B, T, C, H, W)
                        B, T, C, H, W = img.shape

                        # 重塑为(B*T, C, H, W)以适应ResNet
                        img_reshaped = img.reshape(B * T, C, H, W)

                        # 通过RGB模型
                        rgb_model = self.obs_encoder.key_model_map['rgb']
                        feature = rgb_model(img_reshaped)  # (B*T, feature_dim)

                        # 重塑回(B, T*feature_dim)
                        feature = feature.reshape(B, -1)
                        rgb_features.append(feature)

                # 合并RGB特征
                if rgb_features:
                    rgb_features = torch.cat(rgb_features, dim=1)
                else:
                    B = obs_dict[list(obs_dict.keys())[0]].shape[0]
                    device = obs_dict[list(obs_dict.keys())[0]].device
                    rgb_features = torch.empty(B, 0, device=device)

                # 处理低维输入
                low_dim_features = []
                for key in self.obs_encoder.low_dim_keys:
                    if key in obs_dict:
                        feature = obs_dict[key]  # shape: (B, T, D)
                        # 展平为(B, T*D)
                        feature = feature.flatten(start_dim=1)
                        low_dim_features.append(feature)

                # 合并低维特征
                if low_dim_features:
                    low_dim_features = torch.cat(low_dim_features, dim=1)
                else:
                    B = obs_dict[list(obs_dict.keys())[0]].shape[0]
                    device = obs_dict[list(obs_dict.keys())[0]].device
                    low_dim_features = torch.empty(B, 0, device=device)

                # 合并所有特征
                all_features = []
                if rgb_features.shape[1] > 0:
                    all_features.append(rgb_features)
                if low_dim_features.shape[1] > 0:
                    all_features.append(low_dim_features)

                if all_features:
                    final_features = torch.cat(all_features, dim=1)
                else:
                    B = obs_dict[list(obs_dict.keys())[0]].shape[0]
                    device = obs_dict[list(obs_dict.keys())[0]].device
                    final_features = torch.empty(B, 0, device=device)

                return final_features

            def forward(self, face_view, left_wrist_view, right_wrist_view, agent_pos):
                """
                完整的观测编码流程（ONNX兼容）

                Args:
                    face_view: (B, T, C, H, W)
                    left_wrist_view: (B, T, C, H, W)
                    right_wrist_view: (B, T, C, H, W)
                    agent_pos: (B, T, D)

                Returns:
                    encoded_obs: (B, obs_dim)
                """
                # 处理图像（应用ONNX兼容的预处理）
                face_view = self.image_processor(face_view)
                left_wrist_view = self.image_processor(left_wrist_view)
                right_wrist_view = self.image_processor(right_wrist_view)

                # 应用normalizer到agent_pos
                if 'agent_pos' in self.normalizer.params_dict:
                    agent_pos = self.normalizer['agent_pos'].normalize(agent_pos)

                # 重构输入字典
                obs_dict = {
                    'face_view': face_view,
                    'left_wrist_view': left_wrist_view,
                    'right_wrist_view': right_wrist_view,
                    'agent_pos': agent_pos
                }

                # 手动实现obs_encoder的功能，避免transform问题
                encoded_obs = self._manual_obs_encoding(obs_dict)

                # 展平输出
                return encoded_obs.reshape(encoded_obs.shape[0], -1)
        
        wrapped_encoder = ObsEncoderWithNormalizer(self.pytorch_model)
        wrapped_encoder.eval()
        
        print(f"✅ obs_encoder包装器创建成功")
        return wrapped_encoder
    
    def export_obs_encoder(self) -> str:
        """导出obs_encoder ONNX模型"""
        print(f"\n=== 导出obs_encoder ONNX模型 ===")
        
        try:
            # 1. 创建包装器
            wrapped_encoder = self._create_obs_encoder_with_normalizer()
            
            # 2. 创建虚拟输入
            dummy_inputs = (
                torch.randn(1, 1, 3, 480, 640, device=self.device),  # face_view
                torch.randn(1, 1, 3, 480, 640, device=self.device),  # left_wrist_view
                torch.randn(1, 1, 3, 480, 640, device=self.device),  # right_wrist_view
                torch.randn(1, 1, 14, device=self.device)            # agent_pos
            )
            
            # 3. 测试包装器
            with torch.no_grad():
                test_output = wrapped_encoder(*dummy_inputs)
                print(f"   测试输出形状: {test_output.shape}")
            
            # 4. 导出ONNX
            onnx_path = os.path.join(self.output_dir, "obs_encoder_complete.onnx")
            
            print(f"   导出到: {onnx_path}")
            
            torch.onnx.export(
                wrapped_encoder,
                dummy_inputs,
                onnx_path,
                export_params=True,
                opset_version=16,
                do_constant_folding=True,
                input_names=['face_view', 'left_wrist_view', 'right_wrist_view', 'agent_pos'],
                output_names=['encoded_obs'],
                dynamic_axes={
                    'face_view': {0: 'batch_size'},
                    'left_wrist_view': {0: 'batch_size'},
                    'right_wrist_view': {0: 'batch_size'},
                    'agent_pos': {0: 'batch_size'},
                    'encoded_obs': {0: 'batch_size'}
                }
            )
            
            print(f"✅ obs_encoder ONNX导出成功: {onnx_path}")
            return onnx_path
            
        except Exception as e:
            print(f"❌ obs_encoder ONNX导出失败: {e}")
            traceback.print_exc()
            return None
    
    def export_unet(self) -> str:
        """导出UNet ONNX模型"""
        print(f"\n=== 导出UNet ONNX模型 ===")
        
        try:
            # 1. 获取UNet模型
            unet_model = self.pytorch_model.model
            unet_model.eval()
            
            # 2. 创建UNet包装器（处理einops问题）
            class UNetWrapper(torch.nn.Module):
                def __init__(self, unet):
                    super().__init__()
                    self.unet = unet
                
                def forward(self, sample, timestep, global_cond):
                    """
                    UNet前向传播
                    
                    Args:
                        sample: (B, H, T) 噪声样本
                        timestep: (B,) 时间步
                        global_cond: (B, cond_dim) 全局条件
                    
                    Returns:
                        noise_pred: (B, H, T) 预测的噪声
                    """
                    # 转换维度：'b h t -> b t h'
                    sample = sample.permute(0, 2, 1)
                    
                    # 处理时间步
                    if not torch.is_tensor(timestep):
                        timestep = torch.tensor([timestep], dtype=torch.long, device=sample.device)
                    elif len(timestep.shape) == 0:
                        timestep = timestep.unsqueeze(0)
                    
                    timestep = timestep.expand(sample.shape[0])
                    
                    # 时间编码
                    global_feature = self.unet.diffusion_step_encoder(timestep)
                    
                    # 拼接全局条件
                    if global_cond is not None:
                        global_feature = torch.cat([global_feature, global_cond], dim=-1)
                    
                    # UNet处理
                    x = sample
                    h = []
                    
                    # 下采样
                    for resnet, resnet2, downsample in self.unet.down_modules:
                        x = resnet(x, global_feature)
                        x = resnet2(x, global_feature)
                        h.append(x)
                        x = downsample(x)
                    
                    # 中间层
                    for mid_module in self.unet.mid_modules:
                        x = mid_module(x, global_feature)
                    
                    # 上采样
                    for resnet, resnet2, upsample in self.unet.up_modules:
                        x = torch.cat((x, h.pop()), dim=1)
                        x = resnet(x, global_feature)
                        x = resnet2(x, global_feature)
                        x = upsample(x)
                    
                    # 最终卷积
                    x = self.unet.final_conv(x)
                    
                    # 转换维度：'b t h -> b h t'
                    x = x.permute(0, 2, 1)
                    
                    return x
            
            wrapped_unet = UNetWrapper(unet_model)
            wrapped_unet.eval()
            
            # 3. 创建虚拟输入
            dummy_inputs = (
                torch.randn(1, 20, 14, device=self.device),    # sample
                torch.tensor([0], dtype=torch.long, device=self.device),  # timestep
                torch.randn(1, 6158, device=self.device)       # global_cond
            )
            
            # 4. 测试包装器
            with torch.no_grad():
                test_output = wrapped_unet(*dummy_inputs)
                print(f"   测试输出形状: {test_output.shape}")
            
            # 5. 导出ONNX
            onnx_path = os.path.join(self.output_dir, "unet_complete.onnx")
            
            print(f"   导出到: {onnx_path}")
            
            torch.onnx.export(
                wrapped_unet,
                dummy_inputs,
                onnx_path,
                export_params=True,
                opset_version=16,
                do_constant_folding=True,
                input_names=['sample', 'timestep', 'global_cond'],
                output_names=['noise_pred'],
                dynamic_axes={
                    'sample': {0: 'batch_size', 2: 'sequence_length'},
                    'global_cond': {0: 'batch_size'},
                    'noise_pred': {0: 'batch_size', 2: 'sequence_length'}
                }
            )
            
            print(f"✅ UNet ONNX导出成功: {onnx_path}")
            return onnx_path

        except Exception as e:
            print(f"❌ UNet ONNX导出失败: {e}")
            traceback.print_exc()
            return None

    def validate_obs_encoder_onnx(self, onnx_path: str) -> bool:
        """验证obs_encoder ONNX模型的准确性"""
        print(f"\n=== 验证obs_encoder ONNX模型 ===")

        try:
            # 1. 加载ONNX会话
            session = ort.InferenceSession(onnx_path)
            print(f"   ONNX模型加载成功")

            # 2. 获取原始PyTorch obs_encoder和normalizer
            obs_encoder = self.pytorch_model.obs_encoder
            normalizer = self.pytorch_model.normalizer
            rgb_keys = self.pytorch_model.rgb_keys
            obs_encoder.eval()

            # 3. 创建多组测试数据
            test_cases = [
                {
                    'name': '随机输入',
                    'inputs': {
                        'face_view': torch.randn(1, 1, 3, 480, 640, device=self.device),
                        'left_wrist_view': torch.randn(1, 1, 3, 480, 640, device=self.device),
                        'right_wrist_view': torch.randn(1, 1, 3, 480, 640, device=self.device),
                        'agent_pos': torch.randn(1, 1, 14, device=self.device)
                    }
                },
                {
                    'name': '固定输入',
                    'inputs': {
                        'face_view': torch.ones(1, 1, 3, 480, 640, device=self.device) * 0.5,
                        'left_wrist_view': torch.ones(1, 1, 3, 480, 640, device=self.device) * 0.5,
                        'right_wrist_view': torch.ones(1, 1, 3, 480, 640, device=self.device) * 0.5,
                        'agent_pos': torch.zeros(1, 1, 14, device=self.device)
                    }
                },
                {
                    'name': '全零输入',
                    'inputs': {
                        'face_view': torch.zeros(1, 1, 3, 480, 640, device=self.device),
                        'left_wrist_view': torch.zeros(1, 1, 3, 480, 640, device=self.device),
                        'right_wrist_view': torch.zeros(1, 1, 3, 480, 640, device=self.device),
                        'agent_pos': torch.zeros(1, 1, 14, device=self.device)
                    }
                }
            ]

            all_passed = True

            for test_case in test_cases:
                print(f"\n   测试案例: {test_case['name']}")
                inputs = test_case['inputs']

                # PyTorch推理（使用原始模型的完整流程）
                with torch.no_grad():
                    # 应用normalizer（忽略RGB keys）
                    nobs = normalizer.normalize(inputs, ignore_keys=rgb_keys)
                    # 移除instruction键（如果存在）
                    this_nobs = {k: v for k, v in nobs.items() if k != 'instruction'}
                    # 通过obs_encoder
                    pytorch_output = obs_encoder(this_nobs)
                    # 展平输出
                    pytorch_output = pytorch_output.reshape(pytorch_output.shape[0], -1)

                # ONNX推理
                onnx_inputs = {
                    'face_view': inputs['face_view'].cpu().numpy(),
                    'left_wrist_view': inputs['left_wrist_view'].cpu().numpy(),
                    'right_wrist_view': inputs['right_wrist_view'].cpu().numpy(),
                    'agent_pos': inputs['agent_pos'].cpu().numpy()
                }

                onnx_output = session.run(None, onnx_inputs)[0]

                # 比较结果
                pytorch_np = pytorch_output.cpu().numpy()
                diff = np.abs(pytorch_np - onnx_output)

                mse = np.mean(diff ** 2)
                mae = np.mean(diff)
                max_diff = np.max(diff)

                print(f"     PyTorch输出: mean={np.mean(pytorch_np):.6f}, std={np.std(pytorch_np):.6f}")
                print(f"     ONNX输出: mean={np.mean(onnx_output):.6f}, std={np.std(onnx_output):.6f}")
                print(f"     差异: MSE={mse:.8f}, MAE={mae:.8f}, Max={max_diff:.8f}")

                # 判断是否通过
                if mse < 1e-6 and max_diff < 1e-5:
                    print(f"     ✅ 通过（高精度一致）")
                elif mse < 1e-4 and max_diff < 1e-2:
                    print(f"     ✅ 通过（可接受精度）")
                elif mse < 1e-2 and max_diff < 1e-1:
                    print(f"     ✅ 通过（低精度但可用）")
                else:
                    print(f"     ❌ 失败（精度不足）")
                    all_passed = False

            if all_passed:
                print(f"\n✅ obs_encoder ONNX验证通过")
            else:
                print(f"\n❌ obs_encoder ONNX验证失败")

            return all_passed

        except Exception as e:
            print(f"❌ obs_encoder ONNX验证失败: {e}")
            traceback.print_exc()
            return False

    def validate_unet_onnx(self, onnx_path: str) -> bool:
        """验证UNet ONNX模型的准确性"""
        print(f"\n=== 验证UNet ONNX模型 ===")

        try:
            # 1. 加载ONNX会话
            session = ort.InferenceSession(onnx_path)
            print(f"   ONNX模型加载成功")

            # 2. 获取原始UNet模型
            unet_model = self.pytorch_model.model
            unet_model.eval()

            # 3. 创建测试数据
            test_cases = [
                {
                    'name': '随机输入',
                    'inputs': (
                        torch.randn(1, 20, 14, device=self.device),
                        torch.tensor([5], dtype=torch.long, device=self.device),
                        torch.randn(1, 6158, device=self.device)
                    )
                },
                {
                    'name': '零时间步',
                    'inputs': (
                        torch.randn(1, 20, 14, device=self.device),
                        torch.tensor([0], dtype=torch.long, device=self.device),
                        torch.randn(1, 6158, device=self.device)
                    )
                }
            ]

            all_passed = True

            for test_case in test_cases:
                print(f"\n   测试案例: {test_case['name']}")
                sample, timestep, global_cond = test_case['inputs']

                # PyTorch推理（使用原始UNet）
                with torch.no_grad():
                    pytorch_output = unet_model(sample, timestep, global_cond=global_cond)

                # ONNX推理
                onnx_inputs = {
                    'sample': sample.cpu().numpy(),
                    'timestep': timestep.cpu().numpy(),
                    'global_cond': global_cond.cpu().numpy()
                }

                onnx_output = session.run(None, onnx_inputs)[0]

                # 比较结果
                pytorch_np = pytorch_output.cpu().numpy()
                diff = np.abs(pytorch_np - onnx_output)

                mse = np.mean(diff ** 2)
                mae = np.mean(diff)
                max_diff = np.max(diff)

                print(f"     PyTorch输出: mean={np.mean(pytorch_np):.6f}, std={np.std(pytorch_np):.6f}")
                print(f"     ONNX输出: mean={np.mean(onnx_output):.6f}, std={np.std(onnx_output):.6f}")
                print(f"     差异: MSE={mse:.8f}, MAE={mae:.8f}, Max={max_diff:.8f}")

                # 判断是否通过
                if mse < 1e-6 and max_diff < 1e-5:
                    print(f"     ✅ 通过（高精度一致）")
                elif mse < 1e-4 and max_diff < 1e-3:
                    print(f"     ✅ 通过（可接受精度）")
                else:
                    print(f"     ❌ 失败（精度不足）")
                    all_passed = False

            if all_passed:
                print(f"\n✅ UNet ONNX验证通过")
            else:
                print(f"\n❌ UNet ONNX验证失败")

            return all_passed

        except Exception as e:
            print(f"❌ UNet ONNX验证失败: {e}")
            traceback.print_exc()
            return False

    def export_and_validate_all(self) -> Dict[str, bool]:
        """导出并验证所有ONNX模型"""
        print(f"=== 开始完整的ONNX导出和验证流程 ===")

        results = {
            'obs_encoder_export': False,
            'obs_encoder_validate': False,
            'unet_export': False,
            'unet_validate': False
        }

        # 1. 导出obs_encoder
        obs_encoder_path = self.export_obs_encoder()
        if obs_encoder_path:
            results['obs_encoder_export'] = True

            # 验证obs_encoder
            results['obs_encoder_validate'] = self.validate_obs_encoder_onnx(obs_encoder_path)

        # 2. 导出UNet
        unet_path = self.export_unet()
        if unet_path:
            results['unet_export'] = True

            # 验证UNet
            results['unet_validate'] = self.validate_unet_onnx(unet_path)

        # 3. 总结结果
        print(f"\n=== 导出和验证结果总结 ===")
        for key, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"   {key}: {status}")

        all_success = all(results.values())
        if all_success:
            print(f"\n🎉 所有ONNX模型导出和验证成功！")
            print(f"   输出目录: {self.output_dir}")
            print(f"   obs_encoder: obs_encoder_complete.onnx")
            print(f"   unet: unet_complete.onnx")
        else:
            print(f"\n⚠️ 部分ONNX模型导出或验证失败，请检查错误信息")

        return results

    def compare_with_existing_tensorrt(self):
        """与现有TensorRT引擎进行对比"""
        print(f"\n=== 与现有TensorRT引擎对比 ===")

        try:
            # 加载现有TensorRT pipeline
            from tensorrt_diffusion_pipeline import TensorRTDiffusionPipeline

            engine_dir = "test_trt_engines"
            if not os.path.exists(os.path.join(engine_dir, "obs_encoder.trt")):
                print(f"   现有TensorRT引擎不存在: {engine_dir}")
                return

            trt_pipeline = TensorRTDiffusionPipeline(
                engine_dir=engine_dir,
                checkpoint_path=self.checkpoint_path,
                num_inference_steps=10,
                device=self.device,
                verbose=False
            )

            # 创建测试输入
            test_obs = {
                'obs': {
                    'face_view': torch.ones(1, 1, 3, 480, 640, device=self.device) * 0.5,
                    'left_wrist_view': torch.ones(1, 1, 3, 480, 640, device=self.device) * 0.5,
                    'right_wrist_view': torch.ones(1, 1, 3, 480, 640, device=self.device) * 0.5,
                    'agent_pos': torch.zeros(1, 1, 14, device=self.device)
                }
            }

            # 新的正确PyTorch方式
            wrapped_encoder = self._create_obs_encoder_with_normalizer()
            with torch.no_grad():
                correct_pytorch_output = wrapped_encoder(
                    test_obs['obs']['face_view'],
                    test_obs['obs']['left_wrist_view'],
                    test_obs['obs']['right_wrist_view'],
                    test_obs['obs']['agent_pos']
                )

            # 现有TensorRT输出
            trt_output = trt_pipeline._encode_observations(test_obs)

            # 比较
            diff = torch.abs(correct_pytorch_output - trt_output)
            mse = torch.mean(diff ** 2).item()
            mae = torch.mean(diff).item()
            max_diff = torch.max(diff).item()

            print(f"   正确PyTorch输出: mean={torch.mean(correct_pytorch_output).item():.6f}")
            print(f"   现有TensorRT输出: mean={torch.mean(trt_output).item():.6f}")
            print(f"   差异: MSE={mse:.6f}, MAE={mae:.6f}, Max={max_diff:.6f}")

            if mse > 1e-3:
                print(f"   ❌ 现有TensorRT引擎与正确PyTorch存在显著差异")
                print(f"   💡 建议使用新导出的ONNX模型重新构建TensorRT引擎")
            else:
                print(f"   ✅ 现有TensorRT引擎与正确PyTorch基本一致")

        except Exception as e:
            print(f"   对比失败: {e}")


def main():
    """主函数"""
    # 使用新的checkpoint (epoch=0029)
    checkpoint_path = "/workspace/diffusion_policy_workshop/test_weight_load/epoch=0029-train_loss=0.009.ckpt"
    # 备用checkpoint路径（如果新checkpoint有问题）
    # checkpoint_path = "/data/outputs/2025.07.08/03.35.29_entangle_line_threefork_ruyiGAN_resnet50_entangle_line_threefork/checkpoints/epoch=0000-train_loss=0.024.ckpt"

    if not os.path.exists(checkpoint_path):
        print(f"❌ 检查点文件不存在: {checkpoint_path}")
        return False

    try:
        # 创建导出器
        exporter = CorrectONNXExporter(
            checkpoint_path=checkpoint_path,
            output_dir="correct_onnx_models",
            device='cuda'
        )

        # 执行完整的导出和验证流程
        results = exporter.export_and_validate_all()

        # 与现有TensorRT引擎对比
        exporter.compare_with_existing_tensorrt()

        # 返回是否全部成功
        return all(results.values())

    except Exception as e:
        print(f"❌ 导出过程发生错误: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    if success:
        print(f"🎉 ONNX导出和验证完全成功！")
        print(f"💡 可以使用导出的ONNX模型重新构建TensorRT引擎")
    else:
        print(f"❌ ONNX导出或验证失败，请检查错误信息")

    sys.exit(0 if success else 1)
