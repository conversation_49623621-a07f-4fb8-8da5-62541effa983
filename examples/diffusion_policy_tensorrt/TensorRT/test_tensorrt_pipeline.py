#!/usr/bin/env python3
"""
Test script for TensorRT Diffusion Policy Pipeline

This script tests the TensorRT optimized pipeline with the existing engines
and compares performance with PyTorch implementation.
"""

import os
import sys
import time
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Union, Tuple
from diffusion_policy.policy.diffusion_unet_image_policy import DiffusionUnetImagePolicy

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入TensorRT相关模块
try:
    import tensorrt as trt
    from cuda import cudart
    CUDA_AVAILABLE = True
except ImportError:
    print("TensorRT or CUDA runtime not available")
    CUDA_AVAILABLE = False

# 导入TensorRTEngine
from tensorrt_diffusion_pipeline import TensorRTDiffusionPipeline, TensorRTEngine


def create_dummy_policy(checkpoint_path=None):
    """Create a dummy policy for testing."""
    try:
        from diffusion_policy.model.vision.multi_image_obs_encoder import MultiImageObsEncoder
        from diffusion_policy.model.vision.model_getter import get_resnet
        from diffusers.schedulers import DDIMScheduler
        from omegaconf import OmegaConf
        import hydra
        import copy
        import torch
        import os
        
        # 1. 加载checkpoint
        if checkpoint_path and os.path.exists(checkpoint_path):
            print(f"尝试从checkpoint加载模型配置: {checkpoint_path}")
            checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
            print("Checkpoint keys:", checkpoint.keys())
            
            # 2. 优先用cfg.policy实例化policy
            policy = None
            if 'cfg' in checkpoint:
                cfg = checkpoint['cfg']
                if hasattr(cfg, 'policy'):
                    print("使用checkpoint中的cfg.policy实例化policy")
                    policy_cfg = OmegaConf.to_container(cfg.policy, resolve=True)
                    # 兼容shape_meta
                    if 'shape_meta' in policy_cfg:
                        print("使用cfg.policy中的shape_meta")
                    else:
                        # 尝试用当前默认shape_meta
                        shape_meta = {
                            'obs': {
                                'face_view': {'shape': (3, 480, 640), 'type': 'rgb', 'horizon': 1},
                                'left_wrist_view': {'shape': (3, 480, 640), 'type': 'rgb', 'horizon': 1},
                                'right_wrist_view': {'shape': (3, 480, 640), 'type': 'rgb', 'horizon': 1},
                                'agent_pos': {'shape': (14,), 'type': 'low_dim', 'horizon': 1}
                            },
                            'action': {'shape': (14,), 'horizon': 20}
                        }
                        policy_cfg['shape_meta'] = shape_meta
                    
                    # 修改obs_encoder配置，确保与TensorRT一致
                    if 'obs_encoder' in policy_cfg:
                        print("修改obs_encoder配置，确保与TensorRT一致")
                        if isinstance(policy_cfg['obs_encoder'], dict):
                            # 设置为与TensorRT相同的配置
                            policy_cfg['obs_encoder']['resize_shape'] = [480, 640]  # 保持原始尺寸
                            policy_cfg['obs_encoder']['crop_shape'] = [480, 640]    # 保持原始尺寸
                            policy_cfg['obs_encoder']['random_crop'] = False
                            policy_cfg['obs_encoder']['imagenet_norm'] = False      # 不使用ImageNet归一化
                            print(f"已修改obs_encoder配置: resize_shape={policy_cfg['obs_encoder']['resize_shape']}, "
                                  f"crop_shape={policy_cfg['obs_encoder']['crop_shape']}, "
                                  f"imagenet_norm={policy_cfg['obs_encoder']['imagenet_norm']}")
                    
                    policy = hydra.utils.instantiate(policy_cfg)
            if policy is None:
                print("未能从cfg.policy实例化，使用默认结构")
                # fallback: 用默认结构
                shape_meta = {
                    'obs': {
                        'face_view': {'shape': (3, 480, 640), 'type': 'rgb', 'horizon': 1},
                        'left_wrist_view': {'shape': (3, 480, 640), 'type': 'rgb', 'horizon': 1},
                        'right_wrist_view': {'shape': (3, 480, 640), 'type': 'rgb', 'horizon': 1},
                        'agent_pos': {'shape': (14,), 'type': 'low_dim', 'horizon': 1}
                    },
                    'action': {'shape': (14,), 'horizon': 20}
                }
                noise_scheduler = DDIMScheduler(
                    num_train_timesteps=1000,
                    beta_start=0.0001,
                    beta_end=0.02,
                    beta_schedule="linear"
                )
                rgb_model = get_resnet('resnet50', weights=None)
                obs_encoder = MultiImageObsEncoder(
                    shape_meta=shape_meta,
                    rgb_model=rgb_model,
                    resize_shape=[480, 640],  # 保持原始尺寸
                    crop_shape=[480, 640],    # 保持原始尺寸
                    random_crop=False,
                    use_group_norm=False,
                    share_rgb_model=True,
                    imagenet_norm=False  # 只除以255，不应用ImageNet归一化
                )
                from diffusion_policy.policy.diffusion_unet_image_policy import DiffusionUnetImagePolicy
                policy = DiffusionUnetImagePolicy(
                    shape_meta=shape_meta,
                    noise_scheduler=noise_scheduler,
                    obs_encoder=obs_encoder,
                    num_inference_steps=100,
                    obs_as_global_cond=True,
                    diffusion_step_embed_dim=128,
                    down_dims=[256, 512, 1024],
                    kernel_size=5,
                    n_groups=8,
                    cond_predict_scale=True,
                    input_pertub=0.1
                )
            # 3. 设置normalizer（优先用pickles/normalizer）
            normalizer = None
            if 'pickles' in checkpoint and 'normalizer' in checkpoint['pickles']:
                print("使用checkpoint中的pickles/normalizer")
                normalizer = checkpoint['pickles']['normalizer']
                policy.set_normalizer(normalizer)
            else:
                print("未找到normalizer，使用identity normalizer")
                action_dim = policy.action_dim if hasattr(policy, 'action_dim') else 14
                from diffusion_policy.model.common.normalizer import LinearNormalizer, SingleFieldLinearNormalizer
                normalizer = LinearNormalizer()
                normalizer["action"] = SingleFieldLinearNormalizer.create_bi_arm_identity(action_dim=action_dim)
                normalizer["agent_pos"] = SingleFieldLinearNormalizer.create_bi_arm_identity(action_dim=action_dim)
                policy.set_normalizer(normalizer)
            # 4. 加载权重（兼容Lightning格式）
            state_dict = None
            if 'state_dicts' in checkpoint:
                state_dicts = checkpoint['state_dicts']
                if 'model' in state_dicts:
                    state_dict = state_dicts['model']
                    print("从state_dicts['model']中提取权重")
                else:
                    state_dict = state_dicts
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint
            # 5. 加载权重并打印兼容/不兼容key
            print(f"加载权重: {len(state_dict)} 个参数")
            model_keys = set(policy.state_dict().keys())
            compatible_keys = []
            incompatible_keys = []
            for key in state_dict.keys():
                if key in model_keys and state_dict[key].shape == policy.state_dict()[key].shape:
                    compatible_keys.append(key)
                else:
                    incompatible_keys.append(key)
            print(f"✅ 兼容参数: {len(compatible_keys)}")
            print(f"⚠️ 不兼容参数: {len(incompatible_keys)}")
            if len(incompatible_keys) > 0:
                print(f"不兼容参数示例: {incompatible_keys[:5]}")
            missing_keys, unexpected_keys = policy.load_state_dict(state_dict, strict=False)
            if len(missing_keys) > 0:
                print(f"⚠️ 缺失的参数: {missing_keys[:5]}")
            if len(unexpected_keys) > 0:
                print(f"⚠️ 额外的参数: {unexpected_keys[:5]}")
            print("权重加载完成！")
            
            # 检查obs_encoder配置
            if hasattr(policy, 'obs_encoder'):
                obs_encoder = policy.obs_encoder
                print("\n检查最终obs_encoder配置:")
                if hasattr(obs_encoder, 'resize_shape'):
                    print(f"resize_shape: {obs_encoder.resize_shape}")
                if hasattr(obs_encoder, 'crop_shape'):
                    print(f"crop_shape: {obs_encoder.crop_shape}")
                if hasattr(obs_encoder, 'imagenet_norm'):
                    print(f"imagenet_norm: {obs_encoder.imagenet_norm}")
                if hasattr(obs_encoder, 'div255'):
                    print(f"div255: {obs_encoder.div255}")
            
            return policy
        # fallback: 无checkpoint
        print("未提供checkpoint，使用默认结构和随机权重")
        shape_meta = {
            'obs': {
                'face_view': {'shape': (3, 480, 640), 'type': 'rgb', 'horizon': 1},
                'left_wrist_view': {'shape': (3, 480, 640), 'type': 'rgb', 'horizon': 1},
                'right_wrist_view': {'shape': (3, 480, 640), 'type': 'rgb', 'horizon': 1},
                'agent_pos': {'shape': (14,), 'type': 'low_dim', 'horizon': 1}
            },
            'action': {'shape': (14,), 'horizon': 20}
        }
        noise_scheduler = DDIMScheduler(
            num_train_timesteps=1000,
            beta_start=0.0001,
            beta_end=0.02,
            beta_schedule="linear"
        )
        rgb_model = get_resnet('resnet50', weights=None)
        obs_encoder = MultiImageObsEncoder(
            shape_meta=shape_meta,
            rgb_model=rgb_model,
            resize_shape=[480, 640],  # 保持原始尺寸
            crop_shape=[480, 640],    # 保持原始尺寸
            random_crop=False,
            use_group_norm=False,
            share_rgb_model=True,
            imagenet_norm=False       # 只除以255，不应用ImageNet归一化
        )
        from diffusion_policy.policy.diffusion_unet_image_policy import DiffusionUnetImagePolicy
        policy = DiffusionUnetImagePolicy(
            shape_meta=shape_meta,
            noise_scheduler=noise_scheduler,
            obs_encoder=obs_encoder,
            num_inference_steps=100,
            obs_as_global_cond=True,
            diffusion_step_embed_dim=128,
            down_dims=[256, 512, 1024],
            kernel_size=5,
            n_groups=8,
            cond_predict_scale=True,
            input_pertub=0.1
        )
        action_dim = policy.action_dim if hasattr(policy, 'action_dim') else 14
        from diffusion_policy.model.common.normalizer import LinearNormalizer, SingleFieldLinearNormalizer
        normalizer = LinearNormalizer()
        normalizer["action"] = SingleFieldLinearNormalizer.create_bi_arm_identity(action_dim=action_dim)
        normalizer["agent_pos"] = SingleFieldLinearNormalizer.create_bi_arm_identity(action_dim=action_dim)
        policy.set_normalizer(normalizer)
        
        # 检查obs_encoder配置
        if hasattr(policy, 'obs_encoder'):
            obs_encoder = policy.obs_encoder
            print("\n检查最终obs_encoder配置:")
            if hasattr(obs_encoder, 'resize_shape'):
                print(f"resize_shape: {obs_encoder.resize_shape}")
            if hasattr(obs_encoder, 'crop_shape'):
                print(f"crop_shape: {obs_encoder.crop_shape}")
            if hasattr(obs_encoder, 'imagenet_norm'):
                print(f"imagenet_norm: {obs_encoder.imagenet_norm}")
            if hasattr(obs_encoder, 'div255'):
                print(f"div255: {obs_encoder.div255}")
        
        return policy
    except Exception as e:
        print(f"Error creating dummy policy: {e}")
        import traceback
        traceback.print_exc()
        return None


def create_dummy_observations(batch_size=1, device='cuda'):
    """Create dummy observations for testing."""
    obs_data = {
        'face_view': torch.randn(batch_size, 1, 3, 480, 640, device=device),
        'left_wrist_view': torch.randn(batch_size, 1, 3, 480, 640, device=device),
        'right_wrist_view': torch.randn(batch_size, 1, 3, 480, 640, device=device),
        'agent_pos': torch.randn(batch_size, 1, 14, device=device)
    }
    # 添加'obs'键
    return {'obs': obs_data}


def create_zero_init_conditional_sample(policy):
    """创建使用全零初始化的conditional_sample函数"""
    def zero_init_conditional_sample(condition_data, condition_mask, local_cond=None, global_cond=None, generator=None, **kwargs):
        model = policy.model
        scheduler = policy.noise_scheduler

        # 使用全零初始化而不是随机噪声
        trajectory = torch.zeros(
            size=condition_data.shape,
            dtype=condition_data.dtype,
            device=condition_data.device)

        # set step values
        scheduler.set_timesteps(policy.num_inference_steps)

        for t in scheduler.timesteps:
            # 1. apply conditioning
            trajectory[condition_mask] = condition_data[condition_mask]

            # 2. predict model output
            model_output = model(trajectory, t,
                local_cond=local_cond, global_cond=global_cond)

            # 3. compute previous image: x_t -> x_t-1
            trajectory = scheduler.step(
                model_output, t, trajectory,
                generator=generator,
                **kwargs
                ).prev_sample

        # finally make sure conditioning is enforced
        trajectory[condition_mask] = condition_data[condition_mask]

        return trajectory

    return zero_init_conditional_sample


def run_pytorch_with_zero_init(policy, obs_dict):
    """使用全零初始化运行PyTorch policy"""
    # 临时替换PyTorch policy的conditional_sample方法
    original_conditional_sample = policy.conditional_sample
    zero_init_fn = create_zero_init_conditional_sample(policy)

    try:
        # 临时替换方法
        policy.conditional_sample = zero_init_fn

        # 运行推理
        result = policy.predict_action(obs_dict)

        return result
    finally:
        # 恢复原始方法
        policy.conditional_sample = original_conditional_sample


def test_basic_functionality(pipeline):
    """Test basic functionality of TensorRT pipeline."""
    print("\nTesting basic functionality...")
    
    # Create dummy observations
    obs_dict = create_dummy_observations(batch_size=1)
    
    try:
        # Test TensorRT inference
        print("Testing TensorRT inference...")
        result = pipeline.predict_action(obs_dict)
        
        print("TensorRT inference successful!")
        print(f"Action shape: {result['action'].shape}")
        print(f"Action dtype: {result['action'].dtype}")
        print(f"Action device: {result['action'].device}")
        
        # Test PyTorch inference if available
        if pipeline.pytorch_policy is not None:
            print("\nTesting PyTorch inference...")
            with torch.no_grad():
                pytorch_result = run_pytorch_with_zero_init(pipeline.pytorch_policy, obs_dict)

            print("PyTorch inference successful!")
            print(f"Action shape: {pytorch_result['action_pred'].shape}")
            print(f"Action dtype: {pytorch_result['action_pred'].dtype}")
            print(f"Action device: {pytorch_result['action_pred'].device}")
            
            # Compare results
            trt_action = result['action'].cpu()
            pytorch_action = pytorch_result['action_pred'].cpu()
            
            # Ensure shapes match for comparison
            if trt_action.shape != pytorch_action.shape:
                if trt_action.shape == (1, 14, 20) and pytorch_action.shape == (1, 20, 14):
                    pytorch_action = pytorch_action.transpose(1, 2)
                    print("Transposed PyTorch action for comparison")
                elif trt_action.shape == (1, 20, 14) and pytorch_action.shape == (1, 14, 20):
                    trt_action = trt_action.transpose(1, 2)
                    print("Transposed TensorRT action for comparison")
            
            if trt_action.shape == pytorch_action.shape:
                print("trt_action", trt_action)
                print("pytorch_action", pytorch_action)
                mse = torch.mean((trt_action - pytorch_action) ** 2).item()
                mae = torch.mean(torch.abs(trt_action - pytorch_action)).item()
                max_diff = torch.max(torch.abs(trt_action - pytorch_action)).item()
                
                print("\nComparison:")
                print(f"MSE: {mse:.6f}")
                print(f"MAE: {mae:.6f}")
            else:
                print(f"Shape mismatch: TensorRT {trt_action.shape} vs PyTorch {pytorch_action.shape}")
        
        return True
        
    except Exception as e:
        print(f"Error during basic functionality test: {e}")
        import traceback
        traceback.print_exc()
        return False


def benchmark_different_steps(pipeline, batch_size=1, num_warmup=50, num_runs=100):
    """
    Benchmark TensorRT vs PyTorch performance with different inference steps.
    使用与PyTorch版本完全相同的严格测试方法。
    
    Args:
        pipeline: TensorRT pipeline
        batch_size: Batch size for testing
        num_warmup: Number of warmup runs
        num_runs: Number of timing runs
        
    Returns:
        Performance comparison results for different steps
    """
    print(f"\nBenchmarking TensorRT vs PyTorch with different inference steps (batch_size={batch_size})...")
    
    if pipeline.pytorch_policy is None:
        print("No PyTorch policy available for comparison")
        return {}
    
    # 分析PyTorch模型结构
    print("\n分析PyTorch模型结构")
    policy = pipeline.pytorch_policy
    
    # 确保PyTorch模型处于eval模式
    if policy.training:
        print("将PyTorch模型设置为eval模式")
        policy.eval()
    else:
        print("PyTorch模型已经处于eval模式")
    
    # 检查模型是否有_predict_action方法
    has_predict_action = hasattr(policy, '_predict_action')
    print(f"模型有_predict_action方法: {has_predict_action}")
    
    # 检查模型是否有predict_action方法
    has_predict_action_public = hasattr(policy, 'predict_action')
    print(f"模型有predict_action方法: {has_predict_action_public}")
    
    # 检查模型是否有forward方法
    has_forward = hasattr(policy, 'forward')
    print(f"模型有forward方法: {has_forward}")
    
    # 检查模型的关键属性
    print(f"模型的关键属性:")
    for attr_name in ['obs_encoder', 'model', 'normalizer', 'noise_scheduler', 'num_inference_steps']:
        if hasattr(policy, attr_name):
            attr = getattr(policy, attr_name)
            attr_type = type(attr).__name__
            print(f"  - {attr_name}: {attr_type}")
    
    # 测试多个推理步数
    inference_steps = [10, 20, 50, 100]  # 测试不同的推理步数
    results = {}
    
    # 创建一个固定的观测输入，确保所有测试使用相同的输入
    fixed_obs_dict = create_dummy_observations(batch_size, device=pipeline.device)
    
    # 打印观测数据的详细信息
    print("\n观测数据信息:")
    if 'obs' in fixed_obs_dict:
        for key, value in fixed_obs_dict['obs'].items():
            print(f"  {key}: shape={value.shape}, dtype={value.dtype}, device={value.device}")
    else:
        for key, value in fixed_obs_dict.items():
            print(f"  {key}: shape={value.shape}, dtype={value.dtype}, device={value.device}")
    
    for steps in inference_steps:
        print(f"\n--- Testing {steps} inference steps ---")
        
        # 先设置PyTorch模型的timesteps
        if hasattr(pipeline.pytorch_policy, 'num_inference_steps'):
            pipeline.pytorch_policy.num_inference_steps = steps
            
            # 直接重新设置PyTorch模型的noise_scheduler的timesteps
            if hasattr(pipeline.pytorch_policy, 'noise_scheduler') and hasattr(pipeline.pytorch_policy.noise_scheduler, 'set_timesteps'):
                # 强制重新设置timesteps
                pipeline.pytorch_policy.noise_scheduler.set_timesteps(steps, device=pipeline.device)
                pytorch_timesteps = pipeline.pytorch_policy.noise_scheduler.timesteps
                
                # 获取PyTorch的timesteps
                print(f"PyTorch timesteps: {len(pytorch_timesteps)}")
                print(f"PyTorch timesteps内容: {pytorch_timesteps}")
                
                # 使用相同的timesteps设置TensorRT
                pipeline.num_inference_steps = steps
                pipeline.scheduler.timesteps = pytorch_timesteps.clone()
                
                # 确认两者使用相同的推理步数和timesteps
                print(f"设置推理步数: TensorRT={pipeline.num_inference_steps}, PyTorch={pipeline.pytorch_policy.num_inference_steps}")
                print(f"TensorRT timesteps: {len(pipeline.scheduler.timesteps)}")
                print(f"TensorRT timesteps内容: {pipeline.scheduler.timesteps}")
        
        # 使用固定的观测数据
        obs_dict = fixed_obs_dict
        
        # 为PyTorch准备batch
        pytorch_batch = {}
        if 'obs' in obs_dict:
            pytorch_batch['obs'] = {}
            for k, v in obs_dict['obs'].items():
                pytorch_batch['obs'][k] = v.to(pipeline.pytorch_policy.device)
        else:
            for k, v in obs_dict.items():
                pytorch_batch[k] = v.to(pipeline.pytorch_policy.device)
        
        # 添加action字段，用于forward调用
        pytorch_batch['action'] = torch.zeros((batch_size, 20, 14), device=pipeline.pytorch_policy.device)
        
        # Warmup TensorRT (设置为50步)
        print(f"预热TensorRT {num_warmup} 步...")
        for _ in range(num_warmup):
            _ = pipeline.predict_action(obs_dict)
        
        # Warmup PyTorch (设置为50步)，使用predict_action方法
        print(f"预热PyTorch {num_warmup} 步...")
        for _ in range(num_warmup):
            with torch.no_grad():
                # 使用predict_action方法，而不是forward方法
                _ = pipeline.pytorch_policy.predict_action(pytorch_batch)
        
        torch.cuda.synchronize()
        
        # 实际测试 TensorRT
        print(f"开始测试TensorRT {num_runs} 次推理...")
        trt_times = []
        
        for step in range(num_runs):
            torch.cuda.synchronize()
            start_time = time.time()
            
            result = pipeline.predict_action(obs_dict)
            
            torch.cuda.synchronize()
            end_time = time.time()
            
            inference_time = (end_time - start_time) * 1000  # 转换为毫秒
            trt_times.append(inference_time)
            
            # 与PyTorch版本相同的进度输出
            if (step + 1) % 20 == 0:
                print(f"  TensorRT步骤 {step + 1}/{num_runs}: {inference_time:.2f}ms")
        
        # 实际测试 PyTorch，使用predict_action方法
        print(f"开始测试PyTorch {num_runs} 次推理...")
        pytorch_times = []
        
        for step in range(num_runs):
            torch.cuda.synchronize()
            start_time = time.time()
            
            with torch.no_grad():
                # 使用predict_action方法，而不是forward方法
                result = pipeline.pytorch_policy.predict_action(pytorch_batch)
            
            torch.cuda.synchronize()
            end_time = time.time()
            
            inference_time = (end_time - start_time) * 1000  # 转换为毫秒
            pytorch_times.append(inference_time)
            
            # 与PyTorch版本相同的进度输出
            if (step + 1) % 20 == 0:
                print(f"  PyTorch步骤 {step + 1}/{num_runs}: {inference_time:.2f}ms")
        
        # 统计结果 (与PyTorch版本相同)
        trt_times = np.array(trt_times)
        pytorch_times = np.array(pytorch_times)
        
        trt_mean_time = np.mean(trt_times)
        trt_std_time = np.std(trt_times)
        trt_min_time = np.min(trt_times)
        trt_max_time = np.max(trt_times)
        
        pytorch_mean_time = np.mean(pytorch_times)
        pytorch_std_time = np.std(pytorch_times)
        pytorch_min_time = np.min(pytorch_times)
        pytorch_max_time = np.max(pytorch_times)
        
        # Calculate speedup
        speedup = pytorch_mean_time / trt_mean_time if trt_mean_time > 0 else 0
        
        # 获取最后一次结果进行准确性分析
        # 分别获取TensorRT和PyTorch的结果
        trt_result = pipeline.predict_action(obs_dict)
        trt_action = trt_result['action'].cpu()
        
        with torch.no_grad():
            # 创建一个直接用于PyTorch模型的批次
            if 'obs' in obs_dict:
                pytorch_batch = {'obs': {}}
                for k, v in obs_dict['obs'].items():
                    pytorch_batch['obs'][k] = v.to(pipeline.pytorch_policy.device)
            else:
                pytorch_batch = {}
                for k, v in obs_dict.items():
                    pytorch_batch[k] = v.to(pipeline.pytorch_policy.device)

            # 使用全零初始化运行PyTorch模型
            pytorch_result = run_pytorch_with_zero_init(pipeline.pytorch_policy, pytorch_batch)
        pytorch_action = pytorch_result['action_pred'].cpu()
        
        # 统一输出格式：TensorRT输出是[batch, action_dim, horizon]，PyTorch输出是[batch, horizon, action_dim]
        # 将PyTorch输出转换为与TensorRT相同的格式
        if trt_action.shape != pytorch_action.shape:
            if trt_action.shape == (batch_size, 14, 20) and pytorch_action.shape == (batch_size, 20, 14):
                # PyTorch输出需要转置以匹配TensorRT格式
                pytorch_action = pytorch_action.transpose(1, 2)  # [batch, 20, 14] -> [batch, 14, 20]
                print(f"已调整PyTorch输出格式: {pytorch_action.shape}")
            elif trt_action.shape == (batch_size, 20, 14) and pytorch_action.shape == (batch_size, 14, 20):
                # TensorRT输出需要转置以匹配PyTorch格式
                trt_action = trt_action.transpose(1, 2)  # [batch, 14, 20] -> [batch, 20, 14]
                print(f"已调整TensorRT输出格式: {trt_action.shape}")
        
        # Print results (与PyTorch版本相同的格式)
        print(f"\n=== TensorRT vs PyTorch (步数={steps}) 测试结果 ===")
        print(f"TensorRT - 平均推理时间: {trt_mean_time:.2f} ± {trt_std_time:.2f} ms")
        print(f"TensorRT - 最小推理时间: {trt_min_time:.2f} ms")
        print(f"TensorRT - 最大推理时间: {trt_max_time:.2f} ms")
        print(f"TensorRT - FPS: {1000/trt_mean_time:.2f}")
        
        print(f"PyTorch - 平均推理时间: {pytorch_mean_time:.2f} ± {pytorch_std_time:.2f} ms")
        print(f"PyTorch - 最小推理时间: {pytorch_min_time:.2f} ms")
        print(f"PyTorch - 最大推理时间: {pytorch_max_time:.2f} ms")
        print(f"PyTorch - FPS: {1000/pytorch_mean_time:.2f}")
        
        print(f"TensorRT加速比: {speedup:.2f}x")
        print(f"性能提升: {((pytorch_mean_time - trt_mean_time) / pytorch_mean_time) * 100:.1f}%")
        
        # 准确性分析
        if trt_action.shape == pytorch_action.shape:
            mse = torch.mean((trt_action - pytorch_action) ** 2).item()
            mae = torch.mean(torch.abs(trt_action - pytorch_action)).item()

            print(f"\n=== 准确性分析 ===")
            print(f"MSE: {mse:.6f}")
            print(f"MAE: {mae:.6f}")
        else:
            print(f"\n=== 准确性分析 ===")
            print(f"Shape mismatch: TensorRT {trt_action.shape} vs PyTorch {pytorch_action.shape}")
            mse = mae = float('nan')
        
        results[f'steps_{steps}'] = {
            'tensorrt_ms': trt_mean_time,
            'tensorrt_std_ms': trt_std_time,
            'tensorrt_min_ms': trt_min_time,
            'tensorrt_max_ms': trt_max_time,
            'tensorrt_fps': 1000/trt_mean_time,
            'pytorch_ms': pytorch_mean_time,
            'pytorch_std_ms': pytorch_std_time,
            'pytorch_min_ms': pytorch_min_time,
            'pytorch_max_ms': pytorch_max_time,
            'pytorch_fps': 1000/pytorch_mean_time,
            'speedup': speedup,
            'performance_improvement_percent': ((pytorch_mean_time - trt_mean_time) / pytorch_mean_time) * 100,
            'accuracy': {
                'mse': mse,
                'mae': mae
            },
            'tensorrt_times': trt_times.tolist(),
            'pytorch_times': pytorch_times.tolist()
        }
    
    return results


def benchmark_different_batch_sizes(pipeline, num_warmup=100, num_runs=100):
    """
    Benchmark TensorRT vs PyTorch performance with different batch sizes.
    使用与PyTorch版本完全相同的严格测试方法。
    
    Args:
        pipeline: TensorRT pipeline
        num_warmup: Number of warmup runs
        num_runs: Number of timing runs
        
    Returns:
        Performance comparison results for different batch sizes
    """
    print(f"\nBenchmarking TensorRT vs PyTorch with different batch sizes...")
    
    if pipeline.pytorch_policy is None:
        print("No PyTorch policy available for comparison")
        return {}
    
    # 确保PyTorch模型处于eval模式
    if pipeline.pytorch_policy.training:
        print("将PyTorch模型设置为eval模式")
        pipeline.pytorch_policy.eval()
    else:
        print("PyTorch模型已经处于eval模式")
    
    batch_sizes = [1]  # 只测试batch=1
    results = {}
    
    # 检查engine支持的最大batch size
    max_batch_size = 1
    if hasattr(pipeline, 'max_batch_size'):
        max_batch_size = pipeline.max_batch_size
    elif hasattr(pipeline, 'engines') and 'unet' in pipeline.engines:
        if hasattr(pipeline.engines['unet'], 'max_batch_size'):
            max_batch_size = pipeline.engines['unet'].max_batch_size
    
    # 如果engine只支持batch=1，则只测batch=1
    if max_batch_size == 1:
        print("[信息] 当前TensorRT engine仅支持batch=1，测试batch=1。")
        batch_sizes = [1]
    
    # 确保使用相同的timesteps
    steps = pipeline.num_inference_steps
    if hasattr(pipeline.pytorch_policy, 'noise_scheduler') and hasattr(pipeline.pytorch_policy.noise_scheduler, 'set_timesteps'):
        # 设置PyTorch的timesteps
        pipeline.pytorch_policy.noise_scheduler.set_timesteps(steps, device=pipeline.device)
        pytorch_timesteps = pipeline.pytorch_policy.noise_scheduler.timesteps
        
        # 使用PyTorch的timesteps设置TensorRT
        pipeline.scheduler.timesteps = pytorch_timesteps.clone()
        
        # 确认两者使用相同的timesteps
        print(f"设置相同的timesteps: 步数={steps}")
        print(f"TensorRT timesteps: {len(pipeline.scheduler.timesteps)}")
        print(f"PyTorch timesteps: {len(pipeline.pytorch_policy.noise_scheduler.timesteps)}")
    
    for batch_size in batch_sizes:
        print(f"\n--- Testing batch size {batch_size} ---")
        
        # Create test observations (只创建一次，避免重复分配)
        obs_dict = create_dummy_observations(batch_size, device=pipeline.device)
        
        # Warmup TensorRT (与PyTorch版本相同的预热策略)
        print(f"预热TensorRT {num_warmup} 步...")
        for _ in range(num_warmup):
            _ = pipeline.predict_action(obs_dict)
        
        # Warmup PyTorch (与PyTorch版本相同的预热策略)
        print(f"预热PyTorch {num_warmup} 步...")
        for _ in range(num_warmup):
            with torch.no_grad():
                # 创建一个直接用于PyTorch模型的批次
                if 'obs' in obs_dict:
                    pytorch_batch = {'obs': {}}
                    for k, v in obs_dict['obs'].items():
                        pytorch_batch['obs'][k] = v.to(pipeline.pytorch_policy.device)
                else:
                    pytorch_batch = {}
                    for k, v in obs_dict.items():
                        pytorch_batch[k] = v.to(pipeline.pytorch_policy.device)
                
                # 使用predict_action方法，确保与TensorRT使用相同的API
                _ = pipeline.pytorch_policy.predict_action(pytorch_batch)
        
        torch.cuda.synchronize()
        
        # 实际测试 TensorRT (与PyTorch版本完全相同的测试方法)
        print(f"开始测试TensorRT {num_runs} 次推理...")
        trt_times = []
        
        for step in range(num_runs):
            torch.cuda.synchronize()
            start_time = time.time()
            
            result = pipeline.predict_action(obs_dict)
            
            torch.cuda.synchronize()
            end_time = time.time()
            
            inference_time = (end_time - start_time) * 1000  # 转换为毫秒
            trt_times.append(inference_time)
            
            # 与PyTorch版本相同的进度输出
            if (step + 1) % 20 == 0:
                print(f"  TensorRT步骤 {step + 1}/{num_runs}: {inference_time:.2f}ms")
        
        # 实际测试 PyTorch (与PyTorch版本完全相同的测试方法)
        print(f"开始测试PyTorch {num_runs} 次推理...")
        pytorch_times = []
        
        for step in range(num_runs):
            torch.cuda.synchronize()
            start_time = time.time()
            
            with torch.no_grad():
                # 创建一个直接用于PyTorch模型的批次
                if 'obs' in obs_dict:
                    pytorch_batch = {'obs': {}}
                    for k, v in obs_dict['obs'].items():
                        pytorch_batch['obs'][k] = v.to(pipeline.pytorch_policy.device)
                else:
                    pytorch_batch = {}
                    for k, v in obs_dict.items():
                        pytorch_batch[k] = v.to(pipeline.pytorch_policy.device)
                
                # 使用predict_action方法，确保与TensorRT使用相同的API
                result = pipeline.pytorch_policy.predict_action(pytorch_batch)
            
            torch.cuda.synchronize()
            end_time = time.time()
            
            inference_time = (end_time - start_time) * 1000  # 转换为毫秒
            pytorch_times.append(inference_time)
            
            # 与PyTorch版本相同的进度输出
            if (step + 1) % 20 == 0:
                print(f"  PyTorch步骤 {step + 1}/{num_runs}: {inference_time:.2f}ms")
        
        # 统计结果 (与PyTorch版本相同)
        trt_times = np.array(trt_times)
        pytorch_times = np.array(pytorch_times)
        
        trt_mean_time = np.mean(trt_times)
        trt_std_time = np.std(trt_times)
        trt_min_time = np.min(trt_times)
        trt_max_time = np.max(trt_times)
        
        pytorch_mean_time = np.mean(pytorch_times)
        pytorch_std_time = np.std(pytorch_times)
        pytorch_min_time = np.min(pytorch_times)
        pytorch_max_time = np.max(pytorch_times)
        
        # Calculate speedup
        speedup = pytorch_mean_time / trt_mean_time if trt_mean_time > 0 else 0
        
        # 获取最后一次结果进行准确性分析
        # 分别获取TensorRT和PyTorch的结果
        trt_result = pipeline.predict_action(obs_dict)
        trt_action = trt_result['action'].cpu()
        
        with torch.no_grad():
            # 创建一个直接用于PyTorch模型的批次
            if 'obs' in obs_dict:
                pytorch_batch = {'obs': {}}
                for k, v in obs_dict['obs'].items():
                    pytorch_batch['obs'][k] = v.to(pipeline.pytorch_policy.device)
            else:
                pytorch_batch = {}
                for k, v in obs_dict.items():
                    pytorch_batch[k] = v.to(pipeline.pytorch_policy.device)

            # 使用全零初始化运行PyTorch模型
            pytorch_result = run_pytorch_with_zero_init(pipeline.pytorch_policy, pytorch_batch)
        pytorch_action = pytorch_result['action_pred'].cpu()
        
        # 统一输出格式：TensorRT输出是[batch, action_dim, horizon]，PyTorch输出是[batch, horizon, action_dim]
        # 将PyTorch输出转换为与TensorRT相同的格式
        if trt_action.shape != pytorch_action.shape:
            if trt_action.shape == (batch_size, 14, 20) and pytorch_action.shape == (batch_size, 20, 14):
                # PyTorch输出需要转置以匹配TensorRT格式
                pytorch_action = pytorch_action.transpose(1, 2)  # [batch, 20, 14] -> [batch, 14, 20]
                print(f"已调整PyTorch输出格式: {pytorch_action.shape}")
            elif trt_action.shape == (batch_size, 20, 14) and pytorch_action.shape == (batch_size, 14, 20):
                # TensorRT输出需要转置以匹配PyTorch格式
                trt_action = trt_action.transpose(1, 2)  # [batch, 14, 20] -> [batch, 20, 14]
                print(f"已调整TensorRT输出格式: {trt_action.shape}")
        
        # Print results (与PyTorch版本相同的格式)
        print(f"\n=== TensorRT vs PyTorch (batch_size={batch_size}) 测试结果 ===")
        print(f"TensorRT - 平均推理时间: {trt_mean_time:.2f} ± {trt_std_time:.2f} ms")
        print(f"TensorRT - 最小推理时间: {trt_min_time:.2f} ms")
        print(f"TensorRT - 最大推理时间: {trt_max_time:.2f} ms")
        print(f"TensorRT - FPS: {1000/trt_mean_time:.2f}")
        
        print(f"PyTorch - 平均推理时间: {pytorch_mean_time:.2f} ± {pytorch_std_time:.2f} ms")
        print(f"PyTorch - 最小推理时间: {pytorch_min_time:.2f} ms")
        print(f"PyTorch - 最大推理时间: {pytorch_max_time:.2f} ms")
        print(f"PyTorch - FPS: {1000/pytorch_mean_time:.2f}")
        
        print(f"TensorRT加速比: {speedup:.2f}x")
        print(f"性能提升: {((pytorch_mean_time - trt_mean_time) / pytorch_mean_time) * 100:.1f}%")
        
        # 准确性分析
        if trt_action.shape == pytorch_action.shape:
            mse = torch.mean((trt_action - pytorch_action) ** 2).item()
            mae = torch.mean(torch.abs(trt_action - pytorch_action)).item()
            print(f"\n=== 准确性分析 ===")
            print(f"MSE: {mse:.6f}")
            print(f"MAE: {mae:.6f}")

        else:
            print(f"\n=== 准确性分析 ===")
            print(f"Shape mismatch: TensorRT {trt_action.shape} vs PyTorch {pytorch_action.shape}")
            mse = mae = max_diff = relative_error = float('nan')
        
        results[f'batch_{batch_size}'] = {
            'tensorrt_ms': trt_mean_time,
            'tensorrt_std_ms': trt_std_time,
            'tensorrt_min_ms': trt_min_time,
            'tensorrt_max_ms': trt_max_time,
            'tensorrt_fps': 1000/trt_mean_time,
            'pytorch_ms': pytorch_mean_time,
            'pytorch_std_ms': pytorch_std_time,
            'pytorch_min_ms': pytorch_min_time,
            'pytorch_max_ms': pytorch_max_time,
            'pytorch_fps': 1000/pytorch_mean_time,
            'speedup': speedup,
            'performance_improvement_percent': ((pytorch_mean_time - trt_mean_time) / pytorch_mean_time) * 100,
            'accuracy': {
                'mse': mse,
                'mae': mae
            },
            'tensorrt_times': trt_times.tolist(),
            'pytorch_times': pytorch_times.tolist()
        }
    
    return results


def print_comprehensive_results(step_results, batch_results):
    """
    Print comprehensive benchmark results.
    
    Args:
        step_results: Results from benchmark_different_steps
        batch_results: Results from benchmark_different_batch_sizes
    """
    print("\n" + "=" * 80)
    print("COMPREHENSIVE BENCHMARK RESULTS")
    print("=" * 80 + "\n")
    
    # 1. 性能数据
    if step_results:
        print("1. 推理步数性能比较")
        print("-" * 60)
        steps_list = sorted([int(k.split('_')[1]) for k in step_results.keys()])
        
        # 创建表格
        headers = ["步数", "TensorRT (ms)", "PyTorch (ms)", "加速比", "性能提升"]
        rows = []
        
        for steps in steps_list:
            key = f'steps_{steps}'
            metrics = step_results[key]
            
            trt_time = metrics['tensorrt_ms']
            pytorch_time = metrics['pytorch_ms']
            speedup = metrics['speedup']
            improvement = metrics['performance_improvement_percent']
            
            rows.append([
                f"{steps}",
                f"{trt_time:.2f} ± {metrics['tensorrt_std_ms']:.2f}",
                f"{pytorch_time:.2f} ± {metrics['pytorch_std_ms']:.2f}",
                f"{speedup:.2f}x",
                f"{improvement:.1f}%"
            ])
        
        # 打印表格
        col_widths = [max(len(str(row[i])) for row in [headers] + rows) + 2 for i in range(len(headers))]
        
        # 打印表头
        header_line = ""
        for i, header in enumerate(headers):
            header_line += header.ljust(col_widths[i])
        print(header_line)
        print("-" * sum(col_widths))
        
        # 打印数据行
        for row in rows:
            line = ""
            for i, cell in enumerate(row):
                line += cell.ljust(col_widths[i])
            print(line)
        print()
    
    # 2. 批量大小性能比较
    if batch_results:
        print("\n2. 批量大小性能比较")
        print("-" * 60)
        batch_sizes = sorted([int(k.split('_')[1]) for k in batch_results.keys()])
        
        # 创建表格
        headers = ["批量大小", "TensorRT (ms)", "PyTorch (ms)", "加速比", "性能提升"]
        rows = []
        
        for batch_size in batch_sizes:
            key = f'batch_{batch_size}'
            metrics = batch_results[key]
            
            trt_time = metrics['tensorrt_ms']
            pytorch_time = metrics['pytorch_ms']
            speedup = metrics['speedup']
            improvement = metrics['performance_improvement_percent']
            
            rows.append([
                f"{batch_size}",
                f"{trt_time:.2f} ± {metrics['tensorrt_std_ms']:.2f}",
                f"{pytorch_time:.2f} ± {metrics['pytorch_std_ms']:.2f}",
                f"{speedup:.2f}x",
                f"{improvement:.1f}%"
            ])
        
        # 打印表格
        col_widths = [max(len(str(row[i])) for row in [headers] + rows) + 2 for i in range(len(headers))]
        
        # 打印表头
        header_line = ""
        for i, header in enumerate(headers):
            header_line += header.ljust(col_widths[i])
        print(header_line)
        print("-" * sum(col_widths))
        
        # 打印数据行
        for row in rows:
            line = ""
            for i, cell in enumerate(row):
                line += cell.ljust(col_widths[i])
            print(line)
        print()
    
    # 3. 汇总统计
    # print("\n3. SUMMARY STATISTICS")
    # print("-" * 60)
    
    # 4. 准确性分析
    print("\n3. ACCURACY ANALYSIS")
    print("-" * 60)
    print("MSE (Mean Squared Error): Lower is better")
    print("MAE (Mean Absolute Error): Lower is better")
    print("MaxDiff (Maximum Difference): Lower is better")
    print("RelError (Relative Error): Lower is better")
    
    if step_results:
        print("\n步数准确性比较:")
        headers = ["步数", "MSE", "MAE"]
        rows = []
        
        for steps in steps_list:
            key = f'steps_{steps}'
            metrics = step_results[key]
            accuracy = metrics['accuracy']
            
            rows.append([
                f"{steps}",
                f"{accuracy['mse']:.6f}",
                f"{accuracy['mae']:.6f}"
                # f"{accuracy['max_diff']:.6f}",
                # f"{accuracy.get('relative_error', float('nan')):.6f}"
            ])
        
        # 打印表格
        col_widths = [max(len(str(row[i])) for row in [headers] + rows) + 2 for i in range(len(headers))]
        
        # 打印表头
        header_line = ""
        for i, header in enumerate(headers):
            header_line += header.ljust(col_widths[i])
        print(header_line)
        print("-" * sum(col_widths))
        
        # 打印数据行
        for row in rows:
            line = ""
            for i, cell in enumerate(row):
                line += cell.ljust(col_widths[i])
            print(line)
    
    if False:
    # if batch_results:
        print("\n批量大小准确性比较:")
        headers = ["批量大小", "MSE", "MAE", "MaxDiff", "RelError"]
        rows = []
        
        for batch_size in batch_sizes:
            key = f'batch_{batch_size}'
            metrics = batch_results[key]
            accuracy = metrics['accuracy']
            
            rows.append([
                f"{batch_size}",
                f"{accuracy['mse']:.6f}",
                f"{accuracy['mae']:.6f}"
            ])
        
        # 打印表格
        col_widths = [max(len(str(row[i])) for row in [headers] + rows) + 2 for i in range(len(headers))]
        
        # 打印表头
        header_line = ""
        for i, header in enumerate(headers):
            header_line += header.ljust(col_widths[i])
        print(header_line)
        print("-" * sum(col_widths))
        
        # 打印数据行
        for row in rows:
            line = ""
            for i, cell in enumerate(row):
                line += cell.ljust(col_widths[i])
            print(line)


def inspect_tensorrt_engine(engine_path):
    """检查TensorRT引擎的内部结构"""
    print(f"\n检查TensorRT引擎: {os.path.basename(engine_path)}")
    
    try:
        import tensorrt as trt
        
        # 创建logger
        logger = trt.Logger(trt.Logger.WARNING)
        
        # 加载引擎
        with open(engine_path, 'rb') as f:
            runtime = trt.Runtime(logger)
            engine = runtime.deserialize_cuda_engine(f.read())
        
        if engine is None:
            print("  引擎加载失败")
            return
        
        # 获取基本信息
        print(f"  引擎信息:")
        
        # TensorRT 10.x API可能没有这些属性，需要兼容处理
        try:
            print(f"    - 最大批量大小: {engine.max_batch_size}")
        except AttributeError:
            print(f"    - 最大批量大小: 动态批量大小 (TensorRT 10.x)")
            
        try:
            print(f"    - 最大工作空间大小: {engine.max_workspace_size / (1024**2):.1f} MB")
        except AttributeError:
            print(f"    - 最大工作空间大小: 未知 (TensorRT 10.x)")
            
        try:
            print(f"    - 设备内存大小: {engine.device_memory_size / (1024**2):.1f} MB")
        except AttributeError:
            print(f"    - 设备内存大小: 未知 (TensorRT 10.x)")
        
        # 获取张量数量
        if hasattr(engine, 'num_io_tensors'):
            print(f"    - 张量数量: {engine.num_io_tensors}")
        else:
            print(f"    - 张量数量: {engine.num_bindings}")
        
        # 检查层信息
        try:
            num_layers = engine.num_layers
            print(f"    - 层数量: {num_layers}")
        except:
            print("    - 无法获取层信息")
        
        # 获取输入输出信息
        print(f"\n  张量信息:")
        
        input_names = []
        output_names = []
        
        # 使用TensorRT 10.x API
        if hasattr(engine, 'num_io_tensors'):
            for i in range(engine.num_io_tensors):
                name = engine.get_tensor_name(i)
                mode = engine.get_tensor_mode(name)
                dtype = engine.get_tensor_dtype(name)
                shape = engine.get_tensor_shape(name)
                
                mode_str = "输入" if mode == trt.TensorIOMode.INPUT else "输出"
                dtype_str = str(trt.nptype(dtype))
                
                print(f"    - {name}: {mode_str}, 形状={shape}, 类型={dtype_str}")
                
                if mode == trt.TensorIOMode.INPUT:
                    input_names.append(name)
                else:
                    output_names.append(name)
        # 使用TensorRT 8.x API
        else:
            for i in range(engine.num_bindings):
                name = engine.get_binding_name(i)
                is_input = engine.binding_is_input(i)
                dtype = engine.get_binding_dtype(i)
                shape = engine.get_binding_shape(i)
                
                mode_str = "输入" if is_input else "输出"
                dtype_str = str(trt.nptype(dtype))
                
                print(f"    - {name}: {mode_str}, 形状={shape}, 类型={dtype_str}")
                
                if is_input:
                    input_names.append(name)
                else:
                    output_names.append(name)
        
        print(f"\n  输入: {input_names}")
        print(f"  输出: {output_names}")
        
        # 尝试创建执行上下文
        context = engine.create_execution_context()
        if context is None:
            print("  执行上下文创建失败")
        else:
            print("  执行上下文创建成功")
        
        return engine
        
    except Exception as e:
        print(f"  检查引擎失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def analyze_performance_bottlenecks(step_results):
    """分析性能瓶颈，特别是TensorRT在某些步骤的异常延迟"""
    print("\n===== 性能瓶颈分析 =====")
    
    # 分析TensorRT每步时间异常
    for steps, result in step_results.items():
        if 'tensorrt' in result and 'step_times' in result['tensorrt']:
            step_times = np.array(result['tensorrt']['step_times'])
            
            # 计算平均值和标准差
            mean_time = np.mean(step_times)
            std_time = np.std(step_times)
            
            # 找出异常值（超过平均值+2倍标准差）
            outliers = np.where(step_times > mean_time + 2 * std_time)[0]
            
            if len(outliers) > 0:
                print(f"\n步数 {steps.split('_')[1]} 的TensorRT执行中发现异常延迟:")
                print(f"  平均每步时间: {mean_time:.2f}ms, 标准差: {std_time:.2f}ms")
                print(f"  异常步骤 (>{mean_time + 2*std_time:.2f}ms):")
                
                for idx in outliers:
                    print(f"    步骤 {idx}: {step_times[idx]:.2f}ms (正常时间的 {step_times[idx]/mean_time:.1f}倍)")
                
                # 计算如果没有异常值，平均时间会是多少
                normal_steps = np.delete(step_times, outliers)
                normal_mean = np.mean(normal_steps)
                
                print(f"  如果没有异常值，平均每步时间将为: {normal_mean:.2f}ms")
                print(f"  总执行时间将减少: {np.sum(step_times[outliers] - normal_mean):.2f}ms")
                
                # 分析可能的原因
                print("\n  可能的原因:")
                print("    1. CUDA上下文切换或内核启动延迟")
                print("    2. 第一次执行时的CUDA编译和优化")
                print("    3. GPU内存分配和释放")
                print("    4. 缓存未命中")
                print("    5. 线程同步开销")
                
                # 建议解决方案
                print("\n  建议解决方案:")
                print("    1. 使用CUDA Graph捕获整个推理过程")
                print("    2. 预热引擎，确保所有CUDA内核都已编译")
                print("    3. 预先分配所有需要的GPU内存")
                print("    4. 使用固定内存布局，避免动态内存分配")
                print("    5. 使用异步执行和流同步")
    
    # 分析TensorRT和PyTorch的性能差异
    for steps, result in step_results.items():
        if 'tensorrt' in result and 'pytorch' in result:
            trt_time = result['tensorrt']['total_ms']
            pt_time = result['pytorch']['total_ms']
            
            if trt_time > pt_time:
                print(f"\n步数 {steps.split('_')[1]} 中TensorRT比PyTorch慢:")
                print(f"  TensorRT: {trt_time:.2f}ms")
                print(f"  PyTorch: {pt_time:.2f}ms")
                print(f"  差异: {trt_time - pt_time:.2f}ms ({trt_time/pt_time:.2f}x)")
                
                # 分析各阶段的时间
                trt_encode = result['tensorrt']['encode_ms']
                trt_diffusion = result['tensorrt']['diffusion_ms']
                trt_overhead = result['tensorrt']['overhead_ms']
                
                pt_encode = result['pytorch']['encode_ms']
                pt_diffusion = result['pytorch']['diffusion_ms']
                pt_overhead = result['pytorch']['overhead_ms']
                
                print("\n  各阶段比较:")
                print(f"    编码阶段: TensorRT={trt_encode:.2f}ms vs PyTorch={pt_encode:.2f}ms (差异: {trt_encode-pt_encode:.2f}ms)")
                print(f"    扩散阶段: TensorRT={trt_diffusion:.2f}ms vs PyTorch={pt_diffusion:.2f}ms (差异: {trt_diffusion-pt_diffusion:.2f}ms)")
                print(f"    其他开销: TensorRT={trt_overhead:.2f}ms vs PyTorch={pt_overhead:.2f}ms (差异: {trt_overhead-pt_overhead:.2f}ms)")
                
                # 找出主要瓶颈
                if trt_encode - pt_encode > trt_diffusion - pt_diffusion:
                    print("\n  主要瓶颈: 编码阶段")
                elif trt_overhead - pt_overhead > trt_diffusion - pt_diffusion and trt_overhead - pt_overhead > trt_encode - pt_encode:
                    print("\n  主要瓶颈: 其他开销（可能是内存传输或同步）")
                else:
                    print("\n  主要瓶颈: 扩散阶段")
                    
                    # 分析扩散步骤
                    if 'step_times' in result['tensorrt'] and 'step_times' in result['pytorch']:
                        trt_steps = np.array(result['tensorrt']['step_times'])
                        pt_steps = np.array(result['pytorch']['step_times'])
                        
                        # 确保长度一致
                        min_len = min(len(trt_steps), len(pt_steps))
                        trt_steps = trt_steps[:min_len]
                        pt_steps = pt_steps[:min_len]
                        
                        # 计算每步差异
                        step_diffs = trt_steps - pt_steps
                        
                        # 找出差异最大的步骤
                        worst_idx = np.argmax(step_diffs)
                        
                        print(f"\n  扩散步骤分析:")
                        print(f"    差异最大的步骤: {worst_idx}")
                        print(f"    TensorRT步骤 {worst_idx}: {trt_steps[worst_idx]:.2f}ms")
                        print(f"    PyTorch步骤 {worst_idx}: {pt_steps[worst_idx]:.2f}ms")
                        print(f"    差异: {step_diffs[worst_idx]:.2f}ms ({trt_steps[worst_idx]/pt_steps[worst_idx]:.2f}x)")
    

def test_tensorrt_pipeline(checkpoint_path=None):
    """Test the TensorRT pipeline."""
    print("Testing TensorRT Diffusion Policy Pipeline")
    print("=" * 50)
    
    # Check if engines exist
    engine_dir = "/workspace/diffusion_policy_workshop/optimization_tensorrt_dp/correct_trt_engines_working_method"
    # engine_dir = "test_trt_engines"
    obs_engine_path = os.path.join(engine_dir, "obs_encoder.trt")
    unet_engine_path = os.path.join(engine_dir, "unet.trt")
    
    if not os.path.exists(obs_engine_path):
        print(f"Error: obs_encoder engine not found at {obs_engine_path}")
        return False
        
    if not os.path.exists(unet_engine_path):
        print(f"Error: unet engine not found at {unet_engine_path}")
        return False
        
    print(f"Found engines:")
    print(f"  - obs_encoder: {os.path.getsize(obs_engine_path) / 1024 / 1024:.1f} MB")
    print(f"  - unet: {os.path.getsize(unet_engine_path) / 1024 / 1024:.1f} MB")
    
    print("\n检查引擎的内部结构...")
    inspect_tensorrt_engine(obs_engine_path)
    inspect_tensorrt_engine(unet_engine_path)
    
    # Create TensorRT pipeline
    from optimization_tensorrt_dp.tensorrt_diffusion_pipeline import TensorRTDiffusionPipeline
    
    try:
        pipeline = TensorRTDiffusionPipeline(
            engine_dir=engine_dir,
            checkpoint_path=checkpoint_path,
            verbose=True
        )
        # 添加nvtx_profile属性
        pipeline.nvtx_profile = False
        print("TensorRT pipeline created successfully")
    except Exception as e:
        print(f"Error creating TensorRT pipeline: {e}")
        return False
    
    # Test basic functionality
    test_basic_functionality(pipeline)
    
    # Benchmark with different steps
    step_results = benchmark_different_steps(pipeline)
    
    # Benchmark with different batch sizes
    batch_results = benchmark_different_batch_sizes(pipeline)
    
    # Print comprehensive results
    print_comprehensive_results(step_results, batch_results)
    
    # 分析性能瓶颈
    # analyze_performance_bottlenecks(step_results)
    
    return True


def test_ddim_scheduler():
    """Test DDIM scheduler functionality."""
    print("\nTesting DDIM Scheduler")
    print("=" * 30)
    
    try:
        from diffusers.schedulers import DDIMScheduler
        
        # Create scheduler (same as precise_performance_comparison.py)
        scheduler = DDIMScheduler(
            num_train_timesteps=1000,
            beta_start=0.0001,
            beta_end=0.02,
            beta_schedule="linear"
        )
        
        # Set timesteps for 100-step inference
        scheduler.set_timesteps(100, device='cuda')
        
        print(f"DDIM scheduler created successfully")
        print(f"Number of inference steps: {scheduler.num_inference_steps}")
        
        # Test scheduler step
        batch_size = 2
        action_dim = 14
        horizon = 20
        
        # Create dummy tensors (全部放在CPU)
        sample = torch.randn(batch_size, action_dim, horizon)
        model_output = torch.randn(batch_size, action_dim, horizon)
        timestep = torch.tensor([500])  # 保持在CPU
        
        # Run scheduler step
        scheduler_output = scheduler.step(model_output, timestep, sample)
        
        print(f"Scheduler step successful")
        print(f"Output shape: {scheduler_output.prev_sample.shape}")
        print(f"Output device: {scheduler_output.prev_sample.device}")
        
        return True
        
    except Exception as e:
        print(f"Error testing DDIM scheduler: {e}")
        import traceback
        traceback.print_exc()
        return False


def benchmark_different_steps_pytorch_only(pipeline, batch_size=1, num_warmup=100, num_runs=100):
    """
    Benchmark PyTorch performance with different inference steps (FP32).
    使用与precise_performance_comparison.py完全相同的测试方法。
    
    Args:
        pipeline: TensorRT pipeline (for access to pytorch_policy)
        batch_size: Batch size for testing
        num_warmup: Number of warmup runs
        num_runs: Number of timing runs
        
    Returns:
        Performance results for different steps
    """
    print(f"\nBenchmarking PyTorch with different inference steps (batch_size={batch_size})...")
    
    if pipeline.pytorch_policy is None:
        print("No PyTorch policy available")
        return {}
    
    # 确保PyTorch模型处于eval模式
    if pipeline.pytorch_policy.training:
        print("将PyTorch模型设置为eval模式")
        pipeline.pytorch_policy.eval()
    else:
        print("PyTorch模型已经处于eval模式")
    
    inference_steps = [10, 20, 50, 100]  # 测试不同的推理步数
    results = {}
    
    for steps in inference_steps:
        print(f"\n--- Testing {steps} inference steps ---")
        
        # Update PyTorch policy inference steps
        if hasattr(pipeline.pytorch_policy, 'num_inference_steps'):
            pipeline.pytorch_policy.num_inference_steps = steps
            
            # 直接重新设置PyTorch模型的noise_scheduler的timesteps
            if hasattr(pipeline.pytorch_policy, 'noise_scheduler') and hasattr(pipeline.pytorch_policy.noise_scheduler, 'set_timesteps'):
                # 强制重新设置timesteps，确保使用正确数量的steps
                pipeline.pytorch_policy.noise_scheduler.set_timesteps(steps, device=pipeline.device)
                print(f"设置PyTorch推理步数: {steps}, 实际timesteps数量: {len(pipeline.pytorch_policy.noise_scheduler.timesteps)}")
                print(f"PyTorch timesteps内容: {pipeline.pytorch_policy.noise_scheduler.timesteps}")
        
        # Create batch for direct PyTorch policy call
        batch = {
            'obs': {
                'face_view': torch.randn(batch_size, 1, 3, 480, 640, device='cuda'),
                'left_wrist_view': torch.randn(batch_size, 1, 3, 480, 640, device='cuda'),
                'right_wrist_view': torch.randn(batch_size, 1, 3, 480, 640, device='cuda'),
                'agent_pos': torch.randn(batch_size, 1, 14, device='cuda')
            },
            'action': torch.randn(batch_size, 20, 14, device='cuda')
        }
        
        # Warmup (与precise_performance_comparison.py相同)
        print(f"预热 {num_warmup} 步...")
        for _ in range(num_warmup):
            with torch.no_grad():
                _ = pipeline.pytorch_policy(batch, predict=True)
        
        torch.cuda.synchronize()
        
        # 实际测试 (与precise_performance_comparison.py完全相同)
        print(f"开始测试 {num_runs} 次推理...")
        times = []
        
        for step in range(num_runs):
            torch.cuda.synchronize()
            start_time = time.time()
            
            with torch.no_grad():
                result = pipeline.pytorch_policy(batch, predict=True)
            
            torch.cuda.synchronize()
            end_time = time.time()
            
            inference_time = (end_time - start_time) * 1000  # 转换为毫秒
            times.append(inference_time)
            
            # 与precise_performance_comparison.py相同的进度输出
            if (step + 1) % 20 == 0:
                print(f"  步骤 {step + 1}/{num_runs}: {inference_time:.2f}ms")
        
        # 统计结果 (与precise_performance_comparison.py相同)
        times = np.array(times)
        mean_time = np.mean(times)
        std_time = np.std(times)
        min_time = np.min(times)
        max_time = np.max(times)
        
        # 只统计最后一次结果
        pytorch_action = result['action_pred'].cpu()
        
        # Print results (与precise_performance_comparison.py相同)
        print(f"\n=== PyTorch 测试结果 ===")
        print(f"平均推理时间: {mean_time:.2f} ± {std_time:.2f} ms")
        print(f"最小推理时间: {min_time:.2f} ms")
        print(f"最大推理时间: {max_time:.2f} ms")
        print(f"总测试时间: {np.sum(times):.2f} ms")
        print(f"FPS: {1000/mean_time:.2f}")
        print(f"PyTorch action shape: {pytorch_action.shape}")
        print(f"PyTorch action - Min: {pytorch_action.min():.6f}, Max: {pytorch_action.max():.6f}, Mean: {pytorch_action.mean():.6f}, Std: {pytorch_action.std():.6f}")
        
        results[f'steps_{steps}'] = {
            'pytorch_ms': mean_time,
            'std_ms': std_time,
            'min_ms': min_time,
            'max_ms': max_time,
            'total_ms': np.sum(times),
            'fps': 1000/mean_time,
            'times': times.tolist(),
            'action_stats': {
                'min': pytorch_action.min().item(),
                'max': pytorch_action.max().item(),
                'mean': pytorch_action.mean().item(),
                'std': pytorch_action.std().item()
            }
        }
    
    return results


def benchmark_different_batch_sizes_pytorch_only(pipeline, num_warmup=100, num_runs=100):
    """
    Benchmark PyTorch performance with different batch sizes (FP32).
    使用与precise_performance_comparison.py完全相同的测试方法。
    
    Args:
        pipeline: TensorRT pipeline (for access to pytorch_policy)
        num_warmup: Number of warmup runs
        num_runs: Number of timing runs
        
    Returns:
        Performance results for different batch sizes
    """
    print(f"\nBenchmarking PyTorch with different batch sizes...")
    
    if pipeline.pytorch_policy is None:
        print("No PyTorch policy available")
        return {}
    
    # 确保PyTorch模型处于eval模式
    if pipeline.pytorch_policy.training:
        print("将PyTorch模型设置为eval模式")
        pipeline.pytorch_policy.eval()
    else:
        print("PyTorch模型已经处于eval模式")
    
    batch_sizes = [1]
    results = {}
    
    for batch_size in batch_sizes:
        print(f"\n--- Testing batch size {batch_size} ---")
        
        # Create batch for direct policy call (只创建一次，避免重复分配)
        batch = {
            'obs': {
                'face_view': torch.randn(batch_size, 1, 3, 480, 640, device='cuda'),
                'left_wrist_view': torch.randn(batch_size, 1, 3, 480, 640, device='cuda'),
                'right_wrist_view': torch.randn(batch_size, 1, 3, 480, 640, device='cuda'),
                'agent_pos': torch.randn(batch_size, 1, 14, device='cuda')
            },
            'action': torch.randn(batch_size, 20, 14, device='cuda')
        }
        
        # Warmup (与precise_performance_comparison.py相同)
        print(f"预热 {num_warmup} 步...")
        for _ in range(num_warmup):
            with torch.no_grad():
                _ = pipeline.pytorch_policy(batch, predict=True)
        
        torch.cuda.synchronize()
        
        # 实际测试 (与precise_performance_comparison.py完全相同)
        print(f"开始测试 {num_runs} 次推理...")
        times = []
        
        for step in range(num_runs):
            torch.cuda.synchronize()
            start_time = time.time()
            
            with torch.no_grad():
                result = pipeline.pytorch_policy(batch, predict=True)
            
            torch.cuda.synchronize()
            end_time = time.time()
            
            inference_time = (end_time - start_time) * 1000  # 转换为毫秒
            times.append(inference_time)
            
            # 与precise_performance_comparison.py相同的进度输出
            if (step + 1) % 20 == 0:
                print(f"  步骤 {step + 1}/{num_runs}: {inference_time:.2f}ms")
        
        # 统计结果 (与precise_performance_comparison.py相同)
        times = np.array(times)
        mean_time = np.mean(times)
        std_time = np.std(times)
        min_time = np.min(times)
        max_time = np.max(times)
        
        # 只统计最后一次结果
        pytorch_action = result['action_pred'].cpu()
        
        # Print results (与precise_performance_comparison.py相同)
        print(f"\n=== PyTorch (batch_size={batch_size}) 测试结果 ===")
        print(f"平均推理时间: {mean_time:.2f} ± {std_time:.2f} ms")
        print(f"最小推理时间: {min_time:.2f} ms")
        print(f"最大推理时间: {max_time:.2f} ms")
        print(f"总测试时间: {np.sum(times):.2f} ms")
        print(f"FPS: {1000/mean_time:.2f}")
        print(f"PyTorch action shape: {pytorch_action.shape}")
        print(f"PyTorch action - Min: {pytorch_action.min():.6f}, Max: {pytorch_action.max():.6f}, Mean: {pytorch_action.mean():.6f}, Std: {pytorch_action.std():.6f}")
        
        results[f'batch_{batch_size}'] = {
            'pytorch_ms': mean_time,
            'std_ms': std_time,
            'min_ms': min_time,
            'max_ms': max_time,
            'total_ms': np.sum(times),
            'fps': 1000/mean_time,
            'times': times.tolist(),
            'action_stats': {
                'min': pytorch_action.min().item(),
                'mean': pytorch_action.mean().item(),
                'max': pytorch_action.max().item(),
                'std': pytorch_action.std().item()
            }
        }
    
    return results


def print_pytorch_only_results(step_results, batch_results):
    """
    Print PyTorch-only benchmark results.
    
    Args:
        step_results: Results from different inference steps
        batch_results: Results from different batch sizes
    """
    print("\n" + "="*80)
    print("PYTORCH (FP32) BENCHMARK RESULTS")
    print("="*80)
    
    # Print inference steps comparison
    if step_results:
        print("\n1. PERFORMANCE BY INFERENCE STEPS")
        print("-" * 60)
        print(f"{'Steps':<8} {'PyTorch(ms)':<12} {'Min':<10} {'Max':<10} {'Mean':<10} {'Std':<10}")
        print("-" * 60)
        
        for key, metrics in step_results.items():
            steps = key.split('_')[1]
            pytorch_time = metrics['pytorch_ms']
            stats = metrics['action_stats']
            
            print(f"{steps:<8} {pytorch_time:<12.2f} {stats['min']:<10.3f} {stats['max']:<10.3f} {stats['mean']:<10.3f} {stats['std']:<10.3f}")
    
    # Print batch size comparison
    if batch_results:
        print("\n2. PERFORMANCE BY BATCH SIZE")
        print("-" * 60)
        print(f"{'Batch':<8} {'PyTorch(ms)':<12} {'Min':<10} {'Max':<10} {'Mean':<10} {'Std':<10}")
        print("-" * 60)
        
        for key, metrics in batch_results.items():
            batch = key.split('_')[1]
            pytorch_time = metrics['pytorch_ms']
            stats = metrics['action_stats']
            
            print(f"{batch:<8} {pytorch_time:<12.2f} {stats['min']:<10.3f} {stats['max']:<10.3f} {stats['mean']:<10.3f} {stats['std']:<10.3f}")
    
    # Print summary statistics
    print("\n3. SUMMARY STATISTICS")
    print("-" * 40)
    
    if step_results:
        pytorch_times = [metrics['pytorch_ms'] for metrics in step_results.values()]
        print(f"Average PyTorch time (by steps): {sum(pytorch_times)/len(pytorch_times):.2f} ms")
        print(f"Min PyTorch time: {min(pytorch_times):.2f} ms")
        print(f"Max PyTorch time: {max(pytorch_times):.2f} ms")
    
    if batch_results:
        pytorch_times = [metrics['pytorch_ms'] for metrics in batch_results.values()]
        print(f"Average PyTorch time (by batch): {sum(pytorch_times)/len(pytorch_times):.2f} ms")
        print(f"Min PyTorch time: {min(pytorch_times):.2f} ms")
        print(f"Max PyTorch time: {max(pytorch_times):.2f} ms")
    
    print("\n4. FP32 PERFORMANCE ANALYSIS")
    print("-" * 40)
    print("• PyTorch is running with FP32 precision")
    print("• Results show inference time for different configurations")
    print("• Action statistics show output distribution")
    print("• Lower inference time indicates better performance")


def main(checkpoint_path=None):
    """Main test function."""
    print("TensorRT Diffusion Policy Pipeline Test")
    print("=" * 50)
    
    # Set default checkpoint path if not provided
    if checkpoint_path is None:
        checkpoint_path = "/data/outputs/2025.07.08/03.35.29_entangle_line_threefork_ruyiGAN_resnet50_entangle_line_threefork/checkpoints/epoch=0000-train_loss=0.024.ckpt"
    
    try:
        # Check CUDA availability
        if not torch.cuda.is_available():
            print("Error: CUDA not available")
            return False
            
        print(f"CUDA available: {torch.cuda.is_available()}")
        print(f"CUDA device: {torch.cuda.get_device_name()}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # Test DDIM scheduler
        if not test_ddim_scheduler():
            print("DDIM scheduler test failed")
            return False
        
        # Test TensorRT pipeline
        if not test_tensorrt_pipeline(checkpoint_path=checkpoint_path):
            print("TensorRT pipeline test failed")
            return False
        
        print("\nAll tests passed!")
        return True
    except Exception as e:
        print(f"Error in main: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Test TensorRT Diffusion Policy Pipeline')
    parser.add_argument('--checkpoint', type=str, default=None, 
                       help='Path to checkpoint file (optional)')
    
    args = parser.parse_args()
    
    success = main(checkpoint_path=args.checkpoint)
    sys.exit(0 if success else 1) 