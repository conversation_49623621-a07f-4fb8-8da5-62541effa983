#!/usr/bin/env python3
"""
FP16溢出调试工具
精确定位FP16计算中的溢出位置
"""

import torch
import numpy as np
import traceback
from tensorrt_diffusion_pipeline import TensorRTEngine

def analyze_fp16_overflow():
    """分析FP16溢出的具体位置"""
    
    print("🔍 FP16溢出精确定位分析")
    print("=" * 80)
    
    # 加载FP16 UNet引擎
    print("\n📊 加载FP16 UNet引擎...")
    try:
        # 首先确保使用FP16引擎
        engine_path = "correct_trt_engines_working_method/unet.trt"
        engine = TensorRTEngine(engine_path)
        print(f"✅ UNet引擎加载成功: {engine_path}")
    except Exception as e:
        print(f"❌ UNet引擎加载失败: {e}")
        return
    
    # 创建测试输入的数值范围
    test_ranges = [
        {
            'name': '全零输入',
            'sample_range': (0.0, 0.0),
            'global_cond_range': (0.0, 0.0),
            'timestep': 0
        },
        {
            'name': '极小值输入',
            'sample_range': (0.001, 0.001),
            'global_cond_range': (0.001, 0.001),
            'timestep': 0
        },
        {
            'name': '小值输入',
            'sample_range': (0.01, 0.01),
            'global_cond_range': (0.01, 0.01),
            'timestep': 0
        },
        {
            'name': '中等值输入_0.05',
            'sample_range': (0.05, 0.05),
            'global_cond_range': (0.05, 0.05),
            'timestep': 0
        },
        {
            'name': '中等值输入_0.1',
            'sample_range': (0.1, 0.1),
            'global_cond_range': (0.1, 0.1),
            'timestep': 0
        },
        {
            'name': '中等值输入_0.2',
            'sample_range': (0.2, 0.2),
            'global_cond_range': (0.2, 0.2),
            'timestep': 0
        },
        {
            'name': '较大值输入_0.5',
            'sample_range': (0.5, 0.5),
            'global_cond_range': (0.5, 0.5),
            'timestep': 0
        },
        {
            'name': '大值输入_1.0',
            'sample_range': (1.0, 1.0),
            'global_cond_range': (1.0, 1.0),
            'timestep': 0
        },
        {
            'name': '随机小范围',
            'sample_range': (-0.1, 0.1),
            'global_cond_range': (-0.1, 0.1),
            'timestep': 0
        },
        {
            'name': '随机中范围',
            'sample_range': (-0.5, 0.5),
            'global_cond_range': (-0.5, 0.5),
            'timestep': 0
        }
    ]
    
    # 测试不同时间步
    timesteps_to_test = [0, 1, 5, 10, 50, 99]
    
    print(f"\n📊 测试 {len(test_ranges)} 种数值范围 × {len(timesteps_to_test)} 个时间步")
    print("-" * 80)
    
    overflow_cases = []
    normal_cases = []
    
    for test_range in test_ranges:
        for timestep in timesteps_to_test:
            test_name = f"{test_range['name']}_t{timestep}"
            
            try:
                # 创建测试输入
                if test_range['sample_range'][0] == test_range['sample_range'][1]:
                    # 固定值
                    sample = torch.full((1, 20, 14), test_range['sample_range'][0], dtype=torch.float32)
                    global_cond = torch.full((1, 6158), test_range['global_cond_range'][0], dtype=torch.float32)
                else:
                    # 随机范围
                    sample = torch.uniform_(torch.empty(1, 20, 14), 
                                          test_range['sample_range'][0], 
                                          test_range['sample_range'][1])
                    global_cond = torch.uniform_(torch.empty(1, 6158), 
                                               test_range['global_cond_range'][0], 
                                               test_range['global_cond_range'][1])
                
                # 准备输入
                inputs = {
                    'sample': sample.cpu().numpy().astype(np.float32),
                    'timestep': np.array([timestep], dtype=np.int64),
                    'global_cond': global_cond.cpu().numpy().astype(np.float32)
                }
                
                # 运行推理
                outputs = engine.infer(inputs)
                noise_pred = outputs['noise_pred']
                
                # 检查输出
                if isinstance(noise_pred, np.ndarray):
                    has_nan = np.isnan(noise_pred).any()
                    has_inf = np.isinf(noise_pred).any()
                    mean_val = np.mean(noise_pred)
                    std_val = np.std(noise_pred)
                    min_val = np.min(noise_pred)
                    max_val = np.max(noise_pred)
                else:
                    has_nan = torch.isnan(noise_pred).any()
                    has_inf = torch.isinf(noise_pred).any()
                    mean_val = float(noise_pred.mean())
                    std_val = float(noise_pred.std())
                    min_val = float(noise_pred.min())
                    max_val = float(noise_pred.max())
                
                # 分析输入数值特征
                input_sample_mean = np.mean(inputs['sample'])
                input_sample_std = np.std(inputs['sample'])
                input_sample_max = np.max(np.abs(inputs['sample']))
                
                input_cond_mean = np.mean(inputs['global_cond'])
                input_cond_std = np.std(inputs['global_cond'])
                input_cond_max = np.max(np.abs(inputs['global_cond']))
                
                result = {
                    'name': test_name,
                    'timestep': timestep,
                    'input_sample_mean': input_sample_mean,
                    'input_sample_std': input_sample_std,
                    'input_sample_max': input_sample_max,
                    'input_cond_mean': input_cond_mean,
                    'input_cond_std': input_cond_std,
                    'input_cond_max': input_cond_max,
                    'output_mean': mean_val,
                    'output_std': std_val,
                    'output_min': min_val,
                    'output_max': max_val,
                    'has_nan': has_nan,
                    'has_inf': has_inf,
                    'status': 'overflow' if (has_nan or has_inf) else 'normal'
                }
                
                if has_nan or has_inf:
                    overflow_cases.append(result)
                    print(f"❌ {test_name}: 溢出检测")
                    print(f"   输入sample: mean={input_sample_mean:.6f}, std={input_sample_std:.6f}, max={input_sample_max:.6f}")
                    print(f"   输入cond: mean={input_cond_mean:.6f}, std={input_cond_std:.6f}, max={input_cond_max:.6f}")
                    print(f"   输出: mean={mean_val}, std={std_val}, min={min_val}, max={max_val}")
                    if has_nan:
                        print(f"   ⚠️ 包含NaN值")
                    if has_inf:
                        print(f"   ⚠️ 包含Inf值")
                else:
                    normal_cases.append(result)
                    print(f"✅ {test_name}: 正常")
                    
            except Exception as e:
                print(f"💥 {test_name}: 推理错误 - {e}")
                overflow_cases.append({
                    'name': test_name,
                    'timestep': timestep,
                    'status': 'error',
                    'error': str(e)
                })
    
    # 分析溢出模式
    print("\n" + "=" * 80)
    print("📈 溢出模式分析")
    print("=" * 80)
    
    print(f"\n📊 统计结果:")
    print(f"   正常案例: {len(normal_cases)}")
    print(f"   溢出案例: {len(overflow_cases)}")
    print(f"   总测试案例: {len(normal_cases) + len(overflow_cases)}")
    
    if overflow_cases:
        print(f"\n❌ 溢出案例详细分析:")
        
        # 按输入数值范围分组
        overflow_by_range = {}
        for case in overflow_cases:
            if case['status'] == 'overflow':
                range_key = f"sample_max={case.get('input_sample_max', 0):.3f}"
                if range_key not in overflow_by_range:
                    overflow_by_range[range_key] = []
                overflow_by_range[range_key].append(case)
        
        print(f"\n🎯 按输入数值范围分组的溢出:")
        for range_key, cases in overflow_by_range.items():
            print(f"   {range_key}: {len(cases)} 个案例")
            for case in cases[:3]:  # 只显示前3个
                print(f"     - {case['name']}")
        
        # 按时间步分组
        overflow_by_timestep = {}
        for case in overflow_cases:
            if case['status'] == 'overflow':
                timestep = case.get('timestep', 0)
                if timestep not in overflow_by_timestep:
                    overflow_by_timestep[timestep] = []
                overflow_by_timestep[timestep].append(case)
        
        print(f"\n🕐 按时间步分组的溢出:")
        for timestep in sorted(overflow_by_timestep.keys()):
            cases = overflow_by_timestep[timestep]
            print(f"   时间步 {timestep}: {len(cases)} 个案例")
        
        # 找出临界值
        print(f"\n🎯 临界值分析:")
        normal_max_values = [case.get('input_sample_max', 0) for case in normal_cases if case.get('input_sample_max') is not None]
        overflow_min_values = [case.get('input_sample_max', 0) for case in overflow_cases if case.get('input_sample_max') is not None and case['status'] == 'overflow']
        
        if normal_max_values and overflow_min_values:
            safe_threshold = max(normal_max_values)
            danger_threshold = min(overflow_min_values)
            print(f"   安全阈值: {safe_threshold:.6f}")
            print(f"   危险阈值: {danger_threshold:.6f}")
            
            if danger_threshold > safe_threshold:
                print(f"   临界区间: [{safe_threshold:.6f}, {danger_threshold:.6f}]")
            else:
                print(f"   ⚠️ 阈值重叠，问题可能与其他因素相关")
    
    if normal_cases:
        print(f"\n✅ 正常案例特征:")
        normal_sample_max = [case.get('input_sample_max', 0) for case in normal_cases if case.get('input_sample_max') is not None]
        if normal_sample_max:
            print(f"   输入数值范围: 0 ~ {max(normal_sample_max):.6f}")
            print(f"   平均输入最大值: {np.mean(normal_sample_max):.6f}")
    
    return overflow_cases, normal_cases

def test_specific_overflow_case():
    """测试特定的溢出案例"""
    
    print("\n" + "=" * 80)
    print("🎯 特定溢出案例深度分析")
    print("=" * 80)
    
    # 基于之前的发现，测试0.1这个特定值
    engine_path = "correct_trt_engines_working_method/unet.trt"
    engine = TensorRTEngine(engine_path)
    
    # 创建导致溢出的精确输入
    sample = torch.full((1, 20, 14), 0.1, dtype=torch.float32)
    global_cond = torch.full((1, 6158), 0.1, dtype=torch.float32)
    timestep = 0
    
    inputs = {
        'sample': sample.cpu().numpy().astype(np.float32),
        'timestep': np.array([timestep], dtype=np.int64),
        'global_cond': global_cond.cpu().numpy().astype(np.float32)
    }
    
    print(f"🔍 测试导致溢出的精确输入:")
    print(f"   sample: 全部为 0.1")
    print(f"   global_cond: 全部为 0.1")
    print(f"   timestep: {timestep}")
    
    try:
        outputs = engine.infer(inputs)
        noise_pred = outputs['noise_pred']
        
        print(f"\n📊 输出分析:")
        if isinstance(noise_pred, np.ndarray):
            has_nan = np.isnan(noise_pred).any()
            has_inf = np.isinf(noise_pred).any()
            finite_mask = np.isfinite(noise_pred)
            
            print(f"   形状: {noise_pred.shape}")
            print(f"   数据类型: {noise_pred.dtype}")
            print(f"   包含NaN: {has_nan}")
            print(f"   包含Inf: {has_inf}")
            
            if has_nan:
                nan_count = np.isnan(noise_pred).sum()
                nan_ratio = nan_count / noise_pred.size
                print(f"   NaN数量: {nan_count}/{noise_pred.size} ({nan_ratio:.2%})")
                
                # 找到第一个NaN的位置
                nan_indices = np.where(np.isnan(noise_pred))
                if len(nan_indices[0]) > 0:
                    first_nan_pos = (nan_indices[0][0], nan_indices[1][0], nan_indices[2][0])
                    print(f"   第一个NaN位置: {first_nan_pos}")
            
            if has_inf:
                inf_count = np.isinf(noise_pred).sum()
                inf_ratio = inf_count / noise_pred.size
                print(f"   Inf数量: {inf_count}/{noise_pred.size} ({inf_ratio:.2%})")
            
            if finite_mask.any():
                finite_values = noise_pred[finite_mask]
                print(f"   有限值统计:")
                print(f"     数量: {len(finite_values)}")
                print(f"     范围: [{np.min(finite_values):.6f}, {np.max(finite_values):.6f}]")
                print(f"     均值: {np.mean(finite_values):.6f}")
                print(f"     标准差: {np.std(finite_values):.6f}")
        
        # 检查FP16数值范围
        print(f"\n🔍 FP16数值范围分析:")
        print(f"   FP16最大值: {np.finfo(np.float16).max}")
        print(f"   FP16最小正值: {np.finfo(np.float16).tiny}")
        print(f"   FP16精度: {np.finfo(np.float16).eps}")
        
    except Exception as e:
        print(f"❌ 推理失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    # 1. 全面溢出分析
    overflow_cases, normal_cases = analyze_fp16_overflow()
    
    # 2. 特定案例深度分析
    test_specific_overflow_case()
    
    # 3. 总结和建议
    print("\n" + "=" * 80)
    print("💡 FP16溢出问题总结")
    print("=" * 80)
    
    if overflow_cases:
        print("🎯 主要发现:")
        print("   1. FP16溢出与特定的输入数值范围相关")
        print("   2. 均匀的中等值（如0.1）比随机分布更容易导致溢出")
        print("   3. 问题可能出现在UNet的特定层或操作中")
        
        print("\n💡 建议解决方案:")
        print("   1. 使用混合精度：obs_encoder(FP16) + UNet(FP32)")
        print("   2. 在UNet中添加数值稳定性检查")
        print("   3. 调整输入数据的预处理范围")
        print("   4. 升级到CUDA 12.9+以获得更好的FP16支持")
    else:
        print("✅ 未检测到FP16溢出问题")
        print("   当前环境下FP16运行稳定")
