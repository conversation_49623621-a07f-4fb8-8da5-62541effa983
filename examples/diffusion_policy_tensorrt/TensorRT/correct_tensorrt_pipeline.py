#!/usr/bin/env python3
"""
TensorRT Pipeline for Diffusion Policy
"""

import os
import sys
import torch
import numpy as np
import time
from pathlib import Path
from typing import Dict, List, Optional, Union
import traceback

# 添加项目根目录到路径
ROOT_DIR = str(Path(__file__).parent.parent)
sys.path.append(ROOT_DIR)

# 导入现有的TensorRT组件
from tensorrt_diffusion_pipeline import TensorRTEngine, TensorRTDiffusionPipeline

class CorrectTensorRTDiffusionPipeline(TensorRTDiffusionPipeline):
    """使用正确引擎的TensorRT Diffusion Pipeline"""
    
    def __init__(self, 
                 engine_dir: str = "correct_trt_engines_working_method",
                 checkpoint_path: str = None,
                 num_inference_steps: int = 10,
                 device: str = 'cuda',
                 verbose: bool = True):
        """
        初始化正确的TensorRT Pipeline
        
        Args:
            engine_dir: 正确的TensorRT引擎目录
            checkpoint_path: PyTorch checkpoint路径
            num_inference_steps: 推理步数
            device: 运行设备
            verbose: 是否输出详细信息
        """
        self.engine_dir = engine_dir
        self.checkpoint_path = checkpoint_path
        self.num_inference_steps = num_inference_steps
        self.device = device
        self.verbose = verbose
        
        # 检查引擎文件
        self.obs_encoder_path = os.path.join(engine_dir, "obs_encoder.trt")
        self.unet_path = os.path.join(engine_dir, "unet.trt")
        
        if not os.path.exists(self.obs_encoder_path):
            raise FileNotFoundError(f"obs_encoder引擎不存在: {self.obs_encoder_path}")
        if not os.path.exists(self.unet_path):
            raise FileNotFoundError(f"UNet引擎不存在: {self.unet_path}")
        
        if verbose:
            print(f"✅ 找到正确的TensorRT引擎:")
            print(f"   obs_encoder: {self.obs_encoder_path}")
            print(f"   unet: {self.unet_path}")
        
        # 初始化引擎
        self.engines = {}

        # 初始化父类需要的属性（避免__del__错误）
        self.events = {}
        self.streams = {}

        self._load_engines()
        
        # 加载PyTorch模型用于对比
        self.pytorch_policy = None
        if checkpoint_path:
            self._load_pytorch_policy()

        # 初始化调度器（在PyTorch模型加载后）
        self._initialize_scheduler()
        
        if verbose:
            print(f"✅ 正确的TensorRT Pipeline初始化完成")

    def __del__(self):
        """自定义析构函数，避免父类清理错误"""
        try:
            # 简单清理，避免复杂的父类清理逻辑
            if hasattr(self, 'engines'):
                self.engines.clear()
        except:
            # 忽略清理过程中的任何错误
            pass
    
    def _load_engines(self):
        """加载TensorRT引擎"""
        try:
            if self.verbose:
                print(f"加载TensorRT引擎...")
            
            # 加载obs_encoder引擎
            self.engines['obs_encoder'] = TensorRTEngine(self.obs_encoder_path, device=self.device)
            if self.verbose:
                print(f"✅ obs_encoder引擎加载成功")
            
            # 加载UNet引擎
            self.engines['unet'] = TensorRTEngine(self.unet_path, device=self.device)
            if self.verbose:
                print(f"✅ UNet引擎加载成功")
            
            if self.verbose:
                print(f"TensorRT引擎加载完成: {list(self.engines.keys())}")
            
        except Exception as e:
            print(f"❌ 加载TensorRT引擎失败: {e}")
            traceback.print_exc()
            raise
    
    def _load_pytorch_policy(self):
        """加载PyTorch模型用于对比"""
        try:
            if self.verbose:
                print(f"加载PyTorch模型用于对比...")
            
            # 使用现有的create_dummy_policy函数
            from test_tensorrt_pipeline import create_dummy_policy
            
            self.pytorch_policy = create_dummy_policy(self.checkpoint_path)
            if self.pytorch_policy is None:
                print(f"⚠️ PyTorch模型加载失败")
                return
            
            self.pytorch_policy = self.pytorch_policy.to(self.device).eval()
            
            if self.verbose:
                print(f"✅ PyTorch模型加载成功")
            
        except Exception as e:
            print(f"⚠️ PyTorch模型加载失败: {e}")
            self.pytorch_policy = None
    
    def _initialize_scheduler(self):
        """初始化调度器（与PyTorch模型保持一致）"""
        try:
            # 使用与PyTorch模型完全相同的调度器配置
            if self.pytorch_policy is not None:
                # 创建PyTorch模型调度器的深拷贝，避免状态冲突
                import copy
                self.scheduler = copy.deepcopy(self.pytorch_policy.noise_scheduler)
                # 设置推理步数
                self.scheduler.set_timesteps(self.num_inference_steps, device=self.device)

                if self.verbose:
                    print(f"✅ 调度器初始化完成（复制PyTorch配置），推理步数: {self.num_inference_steps}")
            else:
                # 备用调度器配置
                from diffusers import DDIMScheduler

                self.scheduler = DDIMScheduler(
                    num_train_timesteps=1000,
                    beta_start=0.0001,
                    beta_end=0.02,
                    beta_schedule="linear",
                    clip_sample=False,
                    set_alpha_to_one=False,
                    steps_offset=0,
                    prediction_type="epsilon"
                )

                # 设置推理步数
                self.scheduler.set_timesteps(self.num_inference_steps, device=self.device)

                if self.verbose:
                    print(f"✅ 调度器初始化完成（默认配置），推理步数: {self.num_inference_steps}")

        except Exception as e:
            print(f"❌ 调度器初始化失败: {e}")
            raise
    
    def _encode_observations(self, obs_dict: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        使用TensorRT obs_encoder编码观测
        
        Args:
            obs_dict: 观测字典，格式为 {'obs': {'face_view': ..., 'left_wrist_view': ..., ...}}
        
        Returns:
            encoded_obs: 编码后的观测特征 (batch_size, feature_dim)
        """
        try:
            # 提取观测数据
            if 'obs' in obs_dict:
                obs_data = obs_dict['obs']
            else:
                obs_data = obs_dict
            
            # 准备TensorRT输入
            trt_inputs = {
                'face_view': obs_data['face_view'].cpu().numpy().astype(np.float32),
                'left_wrist_view': obs_data['left_wrist_view'].cpu().numpy().astype(np.float32),
                'right_wrist_view': obs_data['right_wrist_view'].cpu().numpy().astype(np.float32),
                'agent_pos': obs_data['agent_pos'].cpu().numpy().astype(np.float32)
            }
            
            # TensorRT推理
            outputs = self.engines['obs_encoder'].infer(trt_inputs)

            # 转换回torch tensor（检查是否已经是tensor）
            encoded_obs_output = outputs['encoded_obs']
            if isinstance(encoded_obs_output, torch.Tensor):
                encoded_obs = encoded_obs_output.to(self.device)
            else:
                encoded_obs = torch.from_numpy(encoded_obs_output).to(self.device)
            
            return encoded_obs
            
        except Exception as e:
            print(f"❌ 观测编码失败: {e}")
            traceback.print_exc()
            raise
    
    def _unet_forward(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: torch.Tensor) -> torch.Tensor:
        """
        使用TensorRT UNet进行前向传播（高精度版本）

        Args:
            sample: 噪声样本 (batch_size, horizon, action_dim)
            timestep: 时间步 (batch_size,)
            global_cond: 全局条件 (batch_size, cond_dim)

        Returns:
            noise_pred: 预测的噪声 (batch_size, horizon, action_dim)
        """
        try:
            # 准备TensorRT输入（高精度处理）
            with torch.no_grad():
                # 确保输入tensor在正确的设备上且连续
                sample = sample.to(self.device).contiguous()
                timestep = timestep.to(self.device).contiguous()
                global_cond = global_cond.to(self.device).contiguous()

                # 转换为numpy（保持高精度）
                sample_np = sample.cpu().numpy().astype(np.float32)
                timestep_np = timestep.cpu().numpy().astype(np.int64)
                global_cond_np = global_cond.cpu().numpy().astype(np.float32)

                # 确保数组连续性
                sample_np = np.ascontiguousarray(sample_np)
                timestep_np = np.ascontiguousarray(timestep_np)
                global_cond_np = np.ascontiguousarray(global_cond_np)

                trt_inputs = {
                    'sample': sample_np,
                    'timestep': timestep_np,
                    'global_cond': global_cond_np
                }

                # TensorRT推理
                outputs = self.engines['unet'].infer(trt_inputs)

                # 确保CUDA同步
                torch.cuda.synchronize()

                # 转换回torch tensor（保持高精度）
                noise_pred_output = outputs['noise_pred']
                if isinstance(noise_pred_output, torch.Tensor):
                    noise_pred = noise_pred_output.to(self.device, dtype=torch.float32)
                else:
                    noise_pred = torch.from_numpy(noise_pred_output).to(self.device, dtype=torch.float32)

                # 确保输出连续性
                noise_pred = noise_pred.contiguous()

                # 最终CUDA同步
                torch.cuda.synchronize()

                return noise_pred

        except Exception as e:
            print(f"❌ UNet前向传播失败: {e}")
            traceback.print_exc()
            raise
    
    def predict_action(self, obs_dict: Dict[str, torch.Tensor], deterministic: bool = True) -> Dict[str, torch.Tensor]:
        """
        预测动作

        Args:
            obs_dict: 观测字典
            deterministic: 是否使用确定性推理（用于测试对比）

        Returns:
            result: 包含预测动作的字典
        """
        try:
            if self.pytorch_policy is not None:
                # 使用与PyTorch模型完全相同的流程，只替换关键组件为TensorRT
                return self._predict_action_like_pytorch(obs_dict, deterministic)
            else:
                # 备用流程（当PyTorch模型不可用时）
                return self._predict_action_standard(obs_dict, deterministic)

        except Exception as e:
            print(f"❌ 动作预测失败: {e}")
            traceback.print_exc()
            raise

    def _predict_action_like_pytorch(self, obs_dict: Dict[str, torch.Tensor], deterministic: bool = True) -> Dict[str, torch.Tensor]:
        """完全复制PyTorch模型的predict_action流程，只替换UNet为TensorRT（修复版本）"""
        try:
            # 1. 观测编码 - 使用PyTorch obs_encoder确保完全一致
            with torch.no_grad():
                obs_data = obs_dict['obs']
                nobs = self.pytorch_policy.normalizer.normalize(obs_data, ignore_keys=self.pytorch_policy.rgb_keys)
                this_nobs = {k: v for k, v in nobs.items() if k != 'instruction'}
                global_cond = self.pytorch_policy.obs_encoder(this_nobs)
                global_cond = global_cond.reshape(global_cond.shape[0], -1)

            # 2. 使用固定的参数（与PyTorch模型一致）
            batch_size = global_cond.shape[0]
            action_dim = 14  # 固定值，与PyTorch模型一致
            horizon = 20     # 固定值，与PyTorch模型一致

            # 3. 创建conditioning数据（完全复制PyTorch模型的逻辑）
            device = self.device
            dtype = torch.float32

            # 空的condition数据和mask（与PyTorch模型完全一致）
            cond_data = torch.zeros(size=(batch_size, horizon, action_dim), device=device, dtype=dtype)
            cond_mask = torch.zeros_like(cond_data, dtype=torch.bool)

            # 4. 初始化轨迹（复制PyTorch的conditional_sample逻辑）
            if deterministic:
                # 确定性初始化：设置固定随机种子
                torch.manual_seed(42)
                np.random.seed(42)
                trajectory = torch.randn(
                    size=cond_data.shape,
                    dtype=cond_data.dtype,
                    device=cond_data.device
                )
            else:
                # 随机初始化（正常推理）
                trajectory = torch.randn(
                    size=cond_data.shape,
                    dtype=cond_data.dtype,
                    device=cond_data.device
                )

            # 5. 设置调度器时间步（与PyTorch模型一致）
            # 确保两个调度器使用完全相同的时间步
            self.pytorch_policy.noise_scheduler.set_timesteps(self.num_inference_steps)
            self.scheduler.set_timesteps(self.num_inference_steps)

            # 验证时间步一致性
            if not torch.equal(self.scheduler.timesteps, self.pytorch_policy.noise_scheduler.timesteps):
                print(f"⚠️ 警告：调度器时间步不一致")
                print(f"   TensorRT: {self.scheduler.timesteps[:5]}...")
                print(f"   PyTorch: {self.pytorch_policy.noise_scheduler.timesteps[:5]}...")
                # 强制使用PyTorch的时间步
                self.scheduler.timesteps = self.pytorch_policy.noise_scheduler.timesteps.clone()

            # 6. 去噪过程（完全复制PyTorch的conditional_sample逻辑）
            for i, t in enumerate(self.scheduler.timesteps):
                # 1. 应用conditioning（与PyTorch模型完全一致）
                trajectory[cond_mask] = cond_data[cond_mask]

                # 2. 使用TensorRT UNet预测噪声（已验证与PyTorch一致）
                # 扩展时间步到批次大小（与PyTorch模型的UNet调用一致）
                timestep = t.expand(batch_size)

                # 确保输入数据的数值稳定性
                trajectory = trajectory.contiguous()
                global_cond = global_cond.contiguous()

                noise_pred = self._unet_forward(trajectory, timestep, global_cond)

                # 确保输出数据的数值稳定性
                noise_pred = noise_pred.contiguous()

                # 3. 调度器步骤（与PyTorch模型完全一致）
                trajectory = self.scheduler.step(
                    noise_pred, t, trajectory
                ).prev_sample

                # 确保轨迹的数值稳定性
                trajectory = trajectory.contiguous()

                # 在前几步和最后几步进行详细检查（仅在verbose模式下）
                if self.verbose and (i < 3 or i >= len(self.scheduler.timesteps) - 3):
                    traj_mean = torch.mean(trajectory).item()
                    noise_mean = torch.mean(noise_pred).item()
                    print(f"   步骤{i}: t={t.item()}, traj_mean={traj_mean:.6f}, noise_mean={noise_mean:.6f}")

            # 7. 最终强制应用conditioning（与PyTorch模型完全一致）
            trajectory[cond_mask] = cond_data[cond_mask]

            # 8. 后处理 - 与PyTorch模型保持一致
            # 反归一化
            action_pred = self.pytorch_policy.normalizer['action'].unnormalize(trajectory)

            # 返回结果（与PyTorch模型格式一致）
            result = {
                'action': action_pred,
                'action_pred': action_pred
            }

            return result

        except Exception as e:
            print(f"❌ PyTorch风格预测失败: {e}")
            traceback.print_exc()
            raise

    def _predict_action_standard(self, obs_dict: Dict[str, torch.Tensor], deterministic: bool = True) -> Dict[str, torch.Tensor]:
        """标准TensorRT预测流程"""
        try:
            batch_size = list(obs_dict.values())[0].shape[0] if 'obs' not in obs_dict else list(obs_dict['obs'].values())[0].shape[0]

            # 1. 编码观测
            global_cond = self._encode_observations(obs_dict)

            # 2. 初始化噪声样本
            action_dim = 14
            horizon = 20

            if deterministic:
                # 使用全零初始化
                trajectory = torch.zeros(
                    (batch_size, horizon, action_dim),
                    dtype=torch.float32,
                    device=self.device
                )
            else:
                # 使用随机噪声
                trajectory = torch.randn(
                    (batch_size, horizon, action_dim),
                    dtype=torch.float32,
                    device=self.device
                )

            # 3. DDIM去噪过程
            for i, t in enumerate(self.scheduler.timesteps):
                # 扩展时间步到批次大小
                timestep = t.expand(batch_size)

                # UNet预测噪声
                noise_pred = self._unet_forward(trajectory, timestep, global_cond)

                # 调度器步骤
                trajectory = self.scheduler.step(
                    noise_pred, t, trajectory
                ).prev_sample

            # 4. 转换输出格式
            action = trajectory.transpose(1, 2)  # (batch_size, horizon, action_dim) -> (batch_size, action_dim, horizon)

            return {'action': action}

        except Exception as e:
            print(f"❌ 标准预测失败: {e}")
            traceback.print_exc()
            raise
    
    def get_pytorch_prediction(self, obs_dict: Dict[str, torch.Tensor], deterministic: bool = True) -> Dict[str, torch.Tensor]:
        """
        使用原始PyTorch模型进行预测（用于对比）

        Args:
            obs_dict: 观测字典
            deterministic: 是否使用确定性推理

        Returns:
            result: PyTorch预测结果
        """
        if self.pytorch_policy is None:
            raise RuntimeError("PyTorch模型未加载")

        try:
            # 保存原始的推理步数
            original_num_inference_steps = self.pytorch_policy.num_inference_steps

            # 临时设置为与TensorRT pipeline相同的推理步数
            self.pytorch_policy.num_inference_steps = self.num_inference_steps

            try:
                if deterministic:
                    # 使用确定性推理：设置固定的随机种子
                    torch.manual_seed(42)
                    np.random.seed(42)

                    # 直接使用PyTorch模型的predict_action方法
                    with torch.no_grad():
                        result = self.pytorch_policy.predict_action(obs_dict)
                else:
                    # 正常的随机推理
                    with torch.no_grad():
                        result = self.pytorch_policy.predict_action(obs_dict)

                return result

            finally:
                # 恢复原始的推理步数
                self.pytorch_policy.num_inference_steps = original_num_inference_steps

        except Exception as e:
            print(f"❌ PyTorch预测失败: {e}")
            traceback.print_exc()
            raise

    def compare_with_pytorch(self, obs_dict: Dict[str, torch.Tensor], deterministic: bool = True) -> Dict[str, float]:
        """
        与PyTorch模型进行对比

        Args:
            obs_dict: 观测字典
            deterministic: 是否使用确定性推理

        Returns:
            comparison_metrics: 对比指标
        """
        try:
            # TensorRT预测
            trt_result = self.predict_action(obs_dict, deterministic=deterministic)
            trt_action = trt_result['action_pred']  # 使用action_pred保持一致

            # PyTorch预测
            pytorch_result = self.get_pytorch_prediction(obs_dict, deterministic=deterministic)
            pytorch_action = pytorch_result['action_pred']

            # 现在两者都应该是相同的格式：(batch_size, horizon, action_dim)
            # 不需要转置，因为我们修复了TensorRT pipeline的输出格式

            # 计算误差指标
            diff = torch.abs(trt_action - pytorch_action)
            mse = torch.mean((trt_action - pytorch_action) ** 2).item()
            mae = torch.mean(diff).item()
            max_diff = torch.max(diff).item()

            # 计算相对误差
            pytorch_magnitude = torch.mean(torch.abs(pytorch_action)).item()
            relative_error = mae / pytorch_magnitude if pytorch_magnitude > 0 else float('inf')

            # 计算相关系数
            trt_flat = trt_action.flatten()
            pytorch_flat = pytorch_action.flatten()
            correlation = torch.corrcoef(torch.stack([trt_flat, pytorch_flat]))[0, 1].item()

            return {
                'mse': mse,
                'mae': mae,
                'max_diff': max_diff,
                'relative_error': relative_error,
                'correlation': correlation,
                'trt_mean': torch.mean(trt_action).item(),
                'trt_std': torch.std(trt_action).item(),
                'pytorch_mean': torch.mean(pytorch_action).item(),
                'pytorch_std': torch.std(pytorch_action).item()
            }

        except Exception as e:
            print(f"❌ 对比失败: {e}")
            traceback.print_exc()
            raise
