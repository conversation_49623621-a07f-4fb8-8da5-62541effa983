#!/bin/bash

# =============================================================================
# TensorRT Pipeline 自动化脚本
# 从PyTorch Checkpoint到TensorRT模型的完整流程
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_CHECKPOINT="/workspace/x2robot_infer/examples/diffusion_policy_tensorrt/TensorRT/epoch=0030-train_loss=0.005.ckpt"
DEFAULT_ONNX_DIR="correct_onnx_models"
DEFAULT_ENGINE_DIR="correct_trt_engines_working_method"
DEFAULT_PRECISION="fp16"

# 函数定义
print_header() {
    echo -e "${BLUE}============================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "TensorRT Pipeline 自动化脚本"
    echo ""
    echo "用法: $0 [选项] [步骤]"
    echo ""
    echo "选项:"
    echo "  -c, --checkpoint PATH    PyTorch checkpoint路径 (默认: $DEFAULT_CHECKPOINT)"
    echo "  -o, --onnx-dir DIR       ONNX模型输出目录 (默认: $DEFAULT_ONNX_DIR)"
    echo "  -e, --engine-dir DIR     TensorRT引擎输出目录 (默认: $DEFAULT_ENGINE_DIR)"
    echo "  -p, --precision TYPE     模型精度 fp32/fp16 (默认: $DEFAULT_PRECISION)"
    echo "  -h, --help               显示此帮助信息"
    echo ""
    echo "步骤 (可以单独执行或组合):"
    echo "  all                      执行完整流程 (默认)"
    echo "  export                   步骤1: 导出ONNX模型"
    echo "  build                    步骤2: 构建TensorRT引擎"
    echo "  test                     步骤3: 测试TensorRT Pipeline"
    echo "  test-real                步骤3: 使用真实数据测试"
    echo "  clean                    清理生成的文件"
    echo ""
    echo "示例:"
    echo "  $0                       # 执行完整流程"
    echo "  $0 export                # 只导出ONNX"
    echo "  $0 build --precision fp16 # 构建FP16引擎"
    echo "  $0 test-real             # 使用真实数据测试"
    echo "  $0 clean                 # 清理文件"
}

# 检查依赖
check_dependencies() {
    print_header "检查依赖环境"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    print_success "Python3 已安装"
    
    # 检查CUDA
    if ! command -v nvidia-smi &> /dev/null; then
        print_error "NVIDIA GPU 或驱动未安装"
        exit 1
    fi
    print_success "NVIDIA GPU 可用"
    
    # 检查必要的Python包
    python3 -c "import torch, tensorrt, onnx, onnxruntime" 2>/dev/null || {
        print_error "缺少必要的Python包 (torch, tensorrt, onnx, onnxruntime)"
        exit 1
    }
    print_success "Python依赖包已安装"
    
    echo ""
}

# 检查文件存在性
check_files() {
    print_header "检查输入文件"
    
    if [[ ! -f "$CHECKPOINT" ]]; then
        print_error "Checkpoint文件不存在: $CHECKPOINT"
        exit 1
    fi
    print_success "Checkpoint文件存在: $(basename $CHECKPOINT)"
    
    # 检查脚本文件
    local scripts=("export_correct_onnx.py" "build_correct_trt_using_working_method.py" "test_correct_tensorrt_pipeline.py")
    for script in "${scripts[@]}"; do
        if [[ ! -f "$script" ]]; then
            print_error "脚本文件不存在: $script"
            exit 1
        fi
    done
    print_success "所有脚本文件存在"
    
    echo ""
}

# 步骤1: 导出ONNX模型
export_onnx() {
    print_header "步骤1: 导出ONNX模型"
    
    print_info "开始导出ONNX模型..."
    print_info "Checkpoint: $CHECKPOINT"
    print_info "输出目录: $ONNX_DIR"
    
    # 修改export_correct_onnx.py中的checkpoint路径
    python3 -c "
import sys
sys.path.append('.')

# 临时修改checkpoint路径
import export_correct_onnx
original_main = export_correct_onnx.main

def modified_main():
    from export_correct_onnx import CorrectONNXExporter
    import os
    
    checkpoint_path = '$CHECKPOINT'
    if not os.path.exists(checkpoint_path):
        print(f'❌ 检查点文件不存在: {checkpoint_path}')
        return False
    
    try:
        exporter = CorrectONNXExporter(
            checkpoint_path=checkpoint_path,
            output_dir='$ONNX_DIR',
            device='cuda'
        )
        results = exporter.export_and_validate_all()
        # 检查是否导出步骤成功（验证失败可以接受）
        export_success = results.get('obs_encoder_export', False) and results.get('unet_export', False)
        return export_success
    except Exception as e:
        print(f'❌ 导出过程发生错误: {e}')
        import traceback
        traceback.print_exc()
        return False

success = modified_main()
sys.exit(0 if success else 1)
"
    
    if [[ $? -eq 0 ]]; then
        print_success "ONNX模型导出成功"
        
        # 显示生成的文件
        if [[ -d "$ONNX_DIR" ]]; then
            print_info "生成的ONNX文件:"
            ls -la "$ONNX_DIR"/*.onnx 2>/dev/null || print_warning "未找到ONNX文件"
        fi
    else
        print_error "ONNX模型导出失败"
        exit 1
    fi
    
    echo ""
}

# 步骤2: 构建TensorRT引擎
build_tensorrt() {
    print_header "步骤2: 构建TensorRT引擎"
    
    print_info "开始构建TensorRT引擎..."
    print_info "ONNX目录: $ONNX_DIR"
    print_info "引擎目录: $ENGINE_DIR"
    print_info "精度: $PRECISION"
    
    python3 build_correct_trt_using_working_method.py \
        --checkpoint "$CHECKPOINT" \
        --onnx-dir "$ONNX_DIR" \
        --engine-dir "$ENGINE_DIR" \
        --precision "$PRECISION"
    
    if [[ $? -eq 0 ]]; then
        print_success "TensorRT引擎构建成功"
        
        # 显示生成的文件
        if [[ -d "$ENGINE_DIR" ]]; then
            print_info "生成的引擎文件:"
            ls -la "$ENGINE_DIR"/*.trt 2>/dev/null || print_warning "未找到引擎文件"
        fi
    else
        print_error "TensorRT引擎构建失败"
        exit 1
    fi
    
    echo ""
}

# 步骤3: 测试TensorRT Pipeline
test_pipeline() {
    local use_real_data=$1
    local test_type="虚拟数据"
    local real_flag=""
    
    if [[ "$use_real_data" == "true" ]]; then
        test_type="真实数据"
        real_flag="--real-data"
    fi
    
    print_header "步骤3: 测试TensorRT Pipeline (使用$test_type)"
    
    print_info "开始测试TensorRT Pipeline..."
    print_info "引擎目录: $ENGINE_DIR"
    print_info "测试类型: $test_type"
    
    python3 test_correct_tensorrt_pipeline.py $real_flag
    
    if [[ $? -eq 0 ]]; then
        print_success "TensorRT Pipeline测试成功"
        
        # 显示生成的可视化文件
        if [[ -f "trajectory_comparison.png" ]]; then
            print_info "生成的可视化文件: trajectory_comparison.png"
        fi
    else
        print_error "TensorRT Pipeline测试失败"
        exit 1
    fi
    
    echo ""
}

# 清理生成的文件
clean_files() {
    print_header "清理生成的文件"
    
    local dirs_to_clean=("$ONNX_DIR" "$ENGINE_DIR")
    local files_to_clean=("trajectory_comparison.png" "multi_sample_trajectory_comparison.png")
    
    for dir in "${dirs_to_clean[@]}"; do
        if [[ -d "$dir" ]]; then
            print_info "删除目录: $dir"
            rm -rf "$dir"
        fi
    done
    
    for file in "${files_to_clean[@]}"; do
        if [[ -f "$file" ]]; then
            print_info "删除文件: $file"
            rm -f "$file"
        fi
    done
    
    print_success "清理完成"
    echo ""
}

# 显示GPU状态
show_gpu_status() {
    print_header "GPU状态"
    nvidia-smi --query-gpu=name,memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits | \
    while IFS=, read -r name mem_used mem_total util; do
        print_info "GPU: $name"
        print_info "显存: ${mem_used}MB / ${mem_total}MB (使用率: ${util}%)"
    done
    echo ""
}

# 解析命令行参数
CHECKPOINT="$DEFAULT_CHECKPOINT"
ONNX_DIR="$DEFAULT_ONNX_DIR"
ENGINE_DIR="$DEFAULT_ENGINE_DIR"
PRECISION="$DEFAULT_PRECISION"
STEPS=("all")

while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--checkpoint)
            CHECKPOINT="$2"
            shift 2
            ;;
        -o|--onnx-dir)
            ONNX_DIR="$2"
            shift 2
            ;;
        -e|--engine-dir)
            ENGINE_DIR="$2"
            shift 2
            ;;
        -p|--precision)
            PRECISION="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        all|export|build|test|test-real|clean)
            if [[ "${STEPS[0]}" == "all" ]]; then
                STEPS=("$1")
            else
                STEPS+=("$1")
            fi
            shift
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主执行流程
main() {
    print_header "TensorRT Pipeline 自动化脚本"
    print_info "开始时间: $(date)"
    
    # 显示配置
    echo -e "${BLUE}配置信息:${NC}"
    echo "  Checkpoint: $CHECKPOINT"
    echo "  ONNX目录: $ONNX_DIR"
    echo "  引擎目录: $ENGINE_DIR"
    echo "  精度: $PRECISION"
    echo "  执行步骤: ${STEPS[*]}"
    echo ""
    
    # 显示GPU状态
    show_gpu_status
    
    # 执行步骤
    for step in "${STEPS[@]}"; do
        case $step in
            all)
                check_dependencies
                check_files
                export_onnx
                build_tensorrt
                test_pipeline false
                ;;
            export)
                check_dependencies
                check_files
                export_onnx
                ;;
            build)
                check_dependencies
                build_tensorrt
                ;;
            test)
                check_dependencies
                test_pipeline false
                ;;
            test-real)
                check_dependencies
                test_pipeline true
                ;;
            clean)
                clean_files
                ;;
        esac
    done
    
    print_header "执行完成"
    print_info "结束时间: $(date)"
    print_success "所有步骤执行成功！"
}

# 切换到脚本目录
cd "$(dirname "$0")"

# 执行主函数
main
