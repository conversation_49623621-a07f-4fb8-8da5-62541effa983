#!/usr/bin/env python3
"""
验证TensorRT Pipeline到Triton的适配
"""

import os
import sys
import torch
import numpy as np
import traceback

# 添加路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
TENSORRT_DIR = os.path.join(ROOT_DIR, "TensorRT")
sys.path.append(TENSORRT_DIR)

def test_tensorrt_pipeline_import():
    """测试TensorRT Pipeline导入"""
    print("🔍 测试TensorRT Pipeline导入...")
    try:
        from correct_tensorrt_pipeline import CorrectTensorRTDiffusionPipeline
        print("✅ TensorRT Pipeline导入成功")
        return True
    except Exception as e:
        print(f"❌ TensorRT Pipeline导入失败: {e}")
        traceback.print_exc()
        return False

def test_tensorrt_pipeline_initialization():
    """测试TensorRT Pipeline初始化"""
    print("\n🔍 测试TensorRT Pipeline初始化...")
    try:
        from correct_tensorrt_pipeline import CorrectTensorRTDiffusionPipeline
        
        # 使用实际的参数
        checkpoint_path = os.path.join(TENSORRT_DIR, "epoch=0030-train_loss=0.005.ckpt")
        engine_dir = os.path.join(TENSORRT_DIR, "correct_trt_engines_working_method")

        # 检查文件是否存在
        if not os.path.exists(checkpoint_path):
            print(f"❌ Checkpoint文件不存在: {checkpoint_path}")
            return False

        if not os.path.exists(engine_dir):
            print(f"❌ 引擎目录不存在: {engine_dir}")
            return False

        # 检查引擎文件
        obs_encoder_path = os.path.join(engine_dir, "obs_encoder.trt")
        unet_path = os.path.join(engine_dir, "unet.trt")

        if not os.path.exists(obs_encoder_path):
            print(f"❌ obs_encoder引擎不存在: {obs_encoder_path}")
            return False

        if not os.path.exists(unet_path):
            print(f"❌ unet引擎不存在: {unet_path}")
            return False
        
        # 初始化Pipeline (注意：engine_dir应该是相对于TensorRT目录的路径)
        # 切换到TensorRT目录执行
        original_cwd = os.getcwd()
        os.chdir(TENSORRT_DIR)

        try:
            pipeline = CorrectTensorRTDiffusionPipeline(
                engine_dir="correct_trt_engines_working_method",
                checkpoint_path="epoch=0030-train_loss=0.005.ckpt",
                num_inference_steps=20,
                device='cuda',
                verbose=False
            )
        finally:
            # 恢复原始工作目录
            os.chdir(original_cwd)
        
        print("✅ TensorRT Pipeline初始化成功")
        return True, pipeline
        
    except Exception as e:
        print(f"❌ TensorRT Pipeline初始化失败: {e}")
        traceback.print_exc()
        return False, None

def test_pipeline_inference(pipeline):
    """测试Pipeline推理"""
    print("\n🔍 测试Pipeline推理...")
    try:
        # 创建测试数据
        batch_size = 1
        obs_dict = {
            'obs': {
                'face_view': torch.rand(batch_size, 1, 3, 480, 640, device='cuda'),
                'left_wrist_view': torch.rand(batch_size, 1, 3, 480, 640, device='cuda'),
                'right_wrist_view': torch.rand(batch_size, 1, 3, 480, 640, device='cuda'),
                'agent_pos': torch.rand(batch_size, 1, 14, device='cuda')
            }
        }
        
        # 执行推理
        with torch.no_grad():
            result = pipeline.predict_action(obs_dict, deterministic=True)
        
        # 检查输出
        if 'action_pred' in result:
            action_pred = result['action_pred']
            print(f"✅ 推理成功，输出形状: {action_pred.shape}")
            print(f"   输出数据类型: {action_pred.dtype}")
            print(f"   输出设备: {action_pred.device}")
            
            # 检查是否有NaN值
            if torch.isnan(action_pred).any():
                print("⚠️  输出包含NaN值")
                return False
            else:
                print("✅ 输出数值正常")
                return True
        else:
            print("❌ 输出中缺少action_pred")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline推理失败: {e}")
        traceback.print_exc()
        return False

def test_triton_model_simulation():
    """模拟Triton模型的数据处理流程"""
    print("\n🔍 测试Triton模型数据处理流程...")
    try:
        # 模拟Triton输入数据
        mock_views = {
            "camera_front": np.random.randint(0, 256, (1, 480, 640, 3), dtype=np.uint8),
            "camera_left": np.random.randint(0, 256, (1, 480, 640, 3), dtype=np.uint8),
            "camera_right": np.random.randint(0, 256, (1, 480, 640, 3), dtype=np.uint8),
        }
        
        mock_actions = {
            "follow1_pos": np.random.rand(7).astype(np.float32),
            "follow2_pos": np.random.rand(7).astype(np.float32),
            "instruction": None
        }
        
        # 模拟数据预处理
        left_agent_data = mock_actions["follow1_pos"]
        right_agent_data = mock_actions["follow2_pos"]
        agent_data = [left_agent_data, right_agent_data]
        agent_data = np.concatenate(agent_data).reshape(1, 14)
        
        obs = {
            "face_view": mock_views["camera_front"],
            "right_wrist_view": mock_views["camera_right"],
            "left_wrist_view": mock_views["camera_left"],
            "agent_pos": agent_data,
        }
        
        # 转换为PyTorch张量
        device = 'cuda'
        rgb_keys = ['face_view', 'left_wrist_view', 'right_wrist_view']
        lowdim_keys = ['agent_pos']
        
        obs_dict = {
            k: torch.from_numpy(v).unsqueeze(0).to(device) 
            for k, v in obs.items() 
            if k in rgb_keys or k in lowdim_keys
        }
        batch = {"obs": obs_dict}
        
        print("✅ Triton数据处理流程模拟成功")
        print(f"   obs_dict keys: {list(obs_dict.keys())}")
        for k, v in obs_dict.items():
            print(f"   {k}: shape={v.shape}, dtype={v.dtype}")
        
        return True, batch
        
    except Exception as e:
        print(f"❌ Triton数据处理流程模拟失败: {e}")
        traceback.print_exc()
        return False, None

def test_end_to_end_integration(pipeline, batch):
    """测试端到端集成"""
    print("\n🔍 测试端到端集成...")
    try:
        # 执行推理
        with torch.no_grad():
            result = pipeline.predict_action(batch, deterministic=True)
        
        # 处理输出
        action_pred = result["action_pred"][0].detach().to("cpu").numpy()
        left_action_pred = action_pred[:, :7]
        right_action_pred = action_pred[:, 7:14]
        
        print(f"✅ 端到端推理成功")
        print(f"   left_action_pred: shape={left_action_pred.shape}")
        print(f"   right_action_pred: shape={right_action_pred.shape}")
        print(f"   输出数值范围: [{action_pred.min():.4f}, {action_pred.max():.4f}]")
        
        # 检查输出是否合理
        if np.isnan(action_pred).any():
            print("❌ 输出包含NaN值")
            return False
        
        if np.isinf(action_pred).any():
            print("❌ 输出包含Inf值")
            return False
        
        print("✅ 输出数值正常")
        return True
        
    except Exception as e:
        print(f"❌ 端到端集成测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 TensorRT Pipeline到Triton适配验证")
    print("=" * 60)
    
    # 测试步骤
    tests = [
        ("导入测试", test_tensorrt_pipeline_import),
        ("初始化测试", test_tensorrt_pipeline_initialization),
    ]
    
    results = {}
    pipeline = None
    
    # 执行基础测试
    for test_name, test_func in tests:
        if test_name == "初始化测试":
            success, pipeline = test_func()
            results[test_name] = success
        else:
            results[test_name] = test_func()
    
    # 如果基础测试通过，继续高级测试
    if all(results.values()) and pipeline is not None:
        results["推理测试"] = test_pipeline_inference(pipeline)
        
        success, batch = test_triton_model_simulation()
        results["数据处理测试"] = success
        
        if success and batch is not None:
            results["端到端集成测试"] = test_end_to_end_integration(pipeline, batch)
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("-" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！TensorRT Pipeline到Triton适配成功")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
