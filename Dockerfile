# 基于 NVIDIA Triton Server 25.03-py3 镜像
FROM nvcr.io/nvidia/tritonserver:25.03-py3

# 设置代理（替换 127.0.0.1:7890 为你的实际代理地址）
ENV http_proxy="http://************:7890"
ENV https_proxy="http://************:7890"

# 更新 apt 并安装 cmake
RUN apt-get update && \
    apt-get install -y --no-install-recommends cmake ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 安装 PyTorch (CUDA 12.6) - 自动使用清华源
RUN pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu126

# 安装 Python 依赖（使用清华源）
RUN pip install --no-cache-dir \
    hydra-core \
    dill \
    zarr==2.18.3 \
    scipy \
    einops \
    diffusers \
    timm \
    transformers \
    robomimic \
    opencv-python-headless \
    wandb \
    accelerate \
    datasets \
    pandas \
    pympler \
    av \
    beartype \
    numcodecs==0.13.1 \
    paramiko \
    scp \
    torchcodec \
    dnspython \
    tritonclient[all]

# 创建模型目录
RUN mkdir -p /x2robot/Models

# 拷贝distilbert-base-uncased文件夹到镜像中
COPY distilbert-base-uncased /x2robot/Models/distilbert-base-uncased

# 复制整个项目根目录到镜像（包含 setup.py）
COPY . /tmp/x2robot_infer

# 安装主库（从根目录运行 setup.py）
RUN cd /tmp/x2robot_infer && \
    pip install .  # 这会读取根目录下的 setup.py

# 单独安装第三方子模块
RUN pip install --no-cache-dir \
    /tmp/x2robot_infer/3rdparty/diffusion_policy \
    /tmp/x2robot_infer/3rdparty/x2robot_dataset

# 复制 examples 到 Triton 目录
COPY ./examples /opt/tritonserver/examples
COPY ./scripts /opt/tritonserver/scripts

# 清理临时文件
RUN rm -rf /tmp/x2robot_infer