# Tutorial

本文档提供了将一个机器人模型适配Triton Server的教程。

本教程分为两节，对应着模型自动化的两个部分，一部分是Triton客户端和服务端的适配和测试，一部分是将服务端配置到自动化部署中。

第一次适配+部署时，不可避免遇到奇奇怪怪的问题以及文字不能说明好的注意点，务必联系@Starrick 共同完成本次适配，以便后续不断优化文档。

## Triton适配与测试

本部分主要教学大家如何将自己训练好的模型，使用Triton Server服务框架持久化部署起来，并使用客户端访问模型服务，获取推理结果。

### 1. 适配Triton Server Backend

首先确认examples中是否存在已适配好的模型，比如最普遍使用的DP，已经适配好，直接采用原先已经打好的镜像即可。

如果自己有对模型做额外的改动，历史窗口大小、灵巧手、各种其他输入等，则需要修改后端的配置。

适配需要先适配服务端，再适配客户端，客户端可以写一个随机生成obs的测试客户端发请求给服务端，看看服务端能否正常返回推理结果。

适配指南参考：`examples/README.md`，请进入`examples/README.md`进行详细阅读！！

### 2. 测试Triton Server Backend

简单适配（不用保证完全正确，在测试步骤细调）完Triton Server的Server后端文件和Client以后，就可以开始测试了，我们需要找一台带docker和4090显卡的开发机。推荐在开发机上，预留一个自己的专门测试Triton Server Backend的容器，用于高效快捷地修改与调试Backend的配置，测试好的稳定配置直接导出。

目前可以在两台开发机上，***********或10.13.10.62上，使用已经存在的Triton镜像 nvcr.io/nvidia/tritonserver:25.03-py3-x2infer-1.03 进行测试。如果这两台开发机上没有账号，联系@Starrick开通后并且提醒Starrick给你的账号添加docker权限。

启动镜像之前，在自己的用户根目录下，创建workspace文件夹，方便容器内外文件交互：

```
# 返回用户根目录
cd ~ 
# 创建与docker共享的目录
mkdir workspace
```

将你的Ckpt拷贝到/home/<USER>/workspace下面，并clone你已经开发好的x2robot_infer仓库（确保你的改动新建分支并提交了）：
```
# 将XXX@*******:/path/to/your/ckpt拷贝到workspace下
rsync -ahv --progress XXX@*******:/path/to/your/ckpt /home/<USER>/workspace/
# git clone仓库
git clone https://gitee.com/x2robot/x2robot_infer.git
# 切换到你的分支
git checkout XXXX
```


使用下面的命令创建容器进行测试：

```
docker run -itd --name XXXX-test-triton \
  --shm-size=1g \
  --ulimit memlock=-1 \
  --ulimit stack=67108864 \
  --network host \
  --gpus '"device=0"' \
  -v /home/<USER>/workspace:/workspace \
  nvcr.io/nvidia/tritonserver:25.03-py3-x2infer-1.03
```

注意几点：

1、`--gpus '"device=0"'`gpu选择显存充足的一块GPU，如果模型需要两个GPU，全选上就行`--gpus '"device=0,1"'`。

2、`-v /home/<USER>/workspace:/workspace` 挂载路径选择个人的路径，最好新建一个workspace文件夹，git clone好所依赖的库：diffusion_policy和x2robot_dataset，下载好ckpt，以及适配好的Triton Server Backend。

3、镜像选择nvcr.io/nvidia/tritonserver:25.03-py3-x2infer-1.03即可，等等其他参数保持不变。

4、将--name XXXX-test-triton里的XXXX改为自己的name，这是容器的名字。


进入容器：
```
docker exec -it XXXX-test-triton /bin/bash
```

注意，接下来的操作，都是在容器以内的！

进入x2robot_infer你新开发的后端的config.pbtxt里，修改环境参数，比如ckpt_path等。

```
# 修改前
parameters {
  key: "ckpt_path"
  value: {
    string_value: "${ckpt_path}"
  }
}
# 修改后
parameters {
  key: "ckpt_path"
  value: {
    string_value: "/workspace/path/to/your/ckpt"
  }
}
```

按照上面的方法，修改好所有的必须参数，也就是你模型推理一定需要用到的参数。

接下来让我们启动Triton Server，启动命令：

```
# 下面的XXXXX为你新适配开发的backend
tritonserver --model-repository=/workspace/x2robot_infer/examples/XXXXX/backend
```

如果端口有冲突，尝试命令：
```
# 修改默认的http、grpc、metric端口，修改后注意客户端的地址，也需要相应修改过去
# 建议只修改端口的中间两位数，统一端口使用段
tritonserver --model-repository=/workspace/x2robot_infer/examples/XXXXX/backend --http-port 8XX0 --grpc-port 8XX1 --metrics-port 8XX2
```

开发完毕后，第一次运行一定会有报错，有报错先尝试丢GPT分析原因，如果不能确定原因，再找@Starrick


## 3. 编写客户端

由于编写机器人客户端涉及到和机器人的Ros API交互，在编写正式的机器人控制客户端前，让我们先检查随机生成一组机器人的状态发给服务端，是否可行。

`examples/diffusion_policy/test_client.py`是随机数据客户端的示例，如果你有输入、输出上的改变，你需要修改这个示例，去适配服务端的协议。

在启动服务后，执行随机数据客户端，检查是否可以成功接受输出。

确保随机数据客户端Work后，可以跳转到 https://gitee.com/x2robot/x2robot_client/tree/master 项目进行真机客户端的适配，需要遵循x2robot_client的设计理念，并与Ros API进行交互。

## 自动化部署适配

本部分主要教学大家如何将适配好的服务端上传到一键部署的系统中。当有新的Ckpt产生时，不需要手动拷贝CKPT、更改Backend配置，在部署平台上简单点击就可以一键部署，加快部署流程。

### 1. 构建并上传容器镜像

关注当前3rdparty的两个依赖库是否和你训练时所使用的版本一致，即diffusion_policy、x2robot_dataset这两个第三方库。以Diffusion Policy举例，检查方法为：

```
# 在该项目的根目录下执行
cd ./3rdparty/diffusion_policy
# 查看当前更新记录，是否是你的训练分支，确保train_workspace可以使用你的ckpt还原
git log
```

如果不一致，在.gitmodules文件中，切换.gitmodules中各个子模块的分支：

```
[submodule "3rdparty/diffusion_policy"]
    path = 3rdparty/diffusion_policy
    url = *************:x2robot/diffusion_policy.git
    branch = dev_dataset_v3  # 修改为目标分支
[submodule "3rdparty/x2robot_dataset"]
    path = 3rdparty/x2robot_dataset
    url = *************:x2robot/x2robot_dataset.git
    branch = dev_dataset_v3  # 修改为目标分支
```

修改完 .gitmodules 文件之后，要更新本地仓库的子模块配置，具体操作如下：
```
git submodule sync
```

在项目根目录下，执行下面的命令构建镜像，切记在构建镜像前，修改scripts/build_image.sh文件中IMAGE_TAG，确保一个独一无二的镜像名。

然后执行下面命令，构建镜像：

```
bash scripts/build_image.sh
```

等待镜像构建完毕后，将镜像推理到镜像仓库：
```
# 首先登录镜像仓库，输入账号密码
docker login harbor.zbl.local
# 账号：admin
# 密码：Harbor12345
```

登录完毕后，推送你刚刚的镜像，等待推送完毕即可。
```
docker push
```

### 2.确认并上传你的Triton后端

这个步骤目的是让你写好的推理后端，放置在集群推理服务统一挂载的`/mnt/backends`下，让所有推理服务都可以访问到你需要部署的服务端。

需要确认你的服务端是否具有规范化的格式。编辑模型配置中存在一项可自定义的`环境变量`，环境变量用于传递模型路径、插值参数等信息，在标准的DP triton server example中，存在这么几行：

```python
        # 从环境变量或配置文件中获取 ckpt_path
        ckpt_path = os.environ.get("CKPT_PATH") or str(model_config["parameters"].get("ckpt_path", {}).get("string_value", ""))
        # 其他参数同理（注意类型转换）
        self.action_interpolate_multiplier = int(
            os.environ.get("ACTION_INTERPOLATE_MULTIPLIER")
            or model_config["parameters"].get("action_interpolate_multiplier", {}).get("string_value", 1)
        )

        self.action_start_ratio = float(
            os.environ.get("ACTION_START_RATIO") or model_config["parameters"].get("action_start_ratio", {}).get("string_value", 0.0)
        )

        self.action_end_ratio = float(
            os.environ.get("ACTION_END_RATIO") or model_config["parameters"].get("action_end_ratio", {}).get("string_value", 1.0)
        )
```

这几行会优先从环境变量获取实际的CKPT路径，而不是model_config（config.pbtxt）里填写的固定值，因此需要注意你需要传递和修改的参数，全部你所需要的是否都有对应的从环境变量获取的行为。

上传前，首先需要在xr-infer-001机器（***********）上拥有一个账号（没有就找@Starrick），然后来到 `/mnt/backends` 下创建自己的目录，比如对于Dummy用户来说：

```
mkdir /mnt/backends/dummy
cd /mnt/backends/dummy
```

进入文件夹后，将你已经适配好的后端拷贝到这里：

```
# 如果适配好的后端在10.13.10.62上，则使用该命令进行拷贝
rsync -ahv --progress dummy@10.13.10.62:/path/to/dummy_backend ./
# 拷贝完毕后，确认后端文件的绝对路径
realpath /mnt/backends/dummy/dummy_backend
```

此时所有模型推理服务就可以感知到你的服务端，之后在部署模型时，填写这个后端路径即可。


### 3.在平台上一键部署

进入 http://factory.x2robot.cn/model/list 点击 “新增模型配置”。

配置名称根据你的模型随意编写。

镜像这一项填写刚刚你完成提交的镜像名。

backendPath这一项填写你刚刚在xr-infer-001保存的Backend路径（容器内的挂载路径和你在xr-infer-001看到的挂载路径是一致的）。

```
# 官方参考模板
/home/<USER>/workspace/x2robot_infer/examples/diffusion_policy/backend
# 修改为你的文件名
/home/<USER>/workspace/x2robot_infer/examples/your_policy/backend
```
（这个在添加共享文件存储后，该过程会发生一些变化，支持不经过PR，自行添加后端，具体关注库的后续更新）

在训练集群确认好需要部署的ckpt，可以被其他用户访问（建议放在/x2robot/share中），这个填写在新增模型配置的“模型路径”中。

env_vars根据你的后端文件中，需要的环境变量参数而决定，如果没有自行添加和改动，参考下面的填写：

```
ACTION_INTERPOLATE_MULTIPLIER: "20"
ACTION_START_RATIO: "0.1"
ACTION_END_RATIO: "0.8"
NVIDIA_DRIVER_CAPABILITIES: "all"
```

多样性选择“不使用多样性”。

此时可以确认编辑，保存模型配置。当你需要使用模型时，打开topic可用状态和模型状态，即可部署模型。

如果希望本地客户端访问测试该模型服务，可以使用域名：

```
# 这里的XXXXX是模型保存配置后，生成的ID，比如10001。
modelXXXXX-service.deploy.svc.cluster.local
```

