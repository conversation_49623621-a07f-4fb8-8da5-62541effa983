# X2Robot Infer

# 项目介绍

X2Robot模型的推理部署仓库，通过Triton Server，在K8s上部署DP、Wall-X等模型。

使用前，请安装本库，位于本库根目录下使用命令：

```bash
pip install .
```

# 模型部署

本项目基于K8s集群进行模型部署，请进入`./examples`文件夹内，按照`./examples/README.md`详细的流程进行模型部署。

# 镜像构建

执行./scripts/build_image.sh，如果有代理，可以使用代理来构建镜像：

```bash

# 不使用代理构建镜像
bash ./scripts/build_image.sh


# 使用代理构建镜像
bash ./scripts/build_image.sh --proxy http://URL:PORT
# 目前可以使用
bash ./scripts/build_image.sh --proxy http://************:7890

```